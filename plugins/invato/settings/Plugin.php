<?php

namespace Invato\Settings;

use Auth;
use Backend;
use Backend\Models\User;
use Backend\Models\UserPreference;
use BackendAuth;
use Cms\Classes\Controller;
use Cms\Classes\PageManager;
use Cms\Models\ThemeData;
use Event;
use Illuminate\Contracts\Http\Kernel;
use Invato\Settings\Classes\BackendRedirectMiddleware;
use Invato\SiteConfiguration\Models\SiteAdminSettings;
use JanVince\SmallGDPR\Models\CookiesSettings;
use OFFLINE\Boxes\Classes\CMS\CmsPageParams;
use OFFLINE\Boxes\Controllers\EditorController;
use OFFLINE\Boxes\Models\Box;
use OFFLINE\Boxes\Models\Page;
use RainLab\Pages\Classes\MenuItem;
use RainLab\Pages\Controllers\Index;
use Redirect;
use Renatio\FormBuilder\Models\Form;
use System\Classes\PluginBase;
use System\Classes\PluginManager;
use System\Classes\SettingsManager;

class Plugin extends PluginBase
{
    public function pluginDetails()
    {
        return [
            'name' => 'Settings',
            'description' => 'Plugin for extending the CMS',
            'author' => 'Invato',
            'icon' => 'icon-leaf',
        ];
    }

    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'Website configuratie',
                'description' => 'Beheer uw bedrijfsgegevens.',
                'category' => SettingsManager::CATEGORY_CMS,
                'icon' => 'icon-paint-brush',
                'url' => Backend::url('cms/themeoptions/update'),
                'permissions' => ['cms.themes'],
            ],
        ];
    }

    public function registerPermissions()
    {
        return [
            'superusers.view_readme' => [
                'tab' => 'Invato',
                'label' => 'View README documentation',
            ],
        ];
    }

    public function registerMailTemplates()
    {
        return [
            'invato.settings:contact-consumer' => 'invato.settings::mail.contact-consumer',
            'invato.settings:contact-company' => 'invato.settings::mail.contact-company',
        ];
    }

    public function boot()
    {
        ThemeData::extend(function ($model) {
            $model->addJsonable('company');
            $model->addJsonable('social');
        });

        Event::listen('backend.form.extendFields', function ($widget) {

            if (
                ! $widget->getController() instanceof Index ||
                ! $widget->model instanceof MenuItem
            ) {
                return;
            }

            $widget->addTabFields([
                'viewBag[icon]' => [
                    'tab' => 'rainlab.pages::lang.menuitem.display_tab',
                    'label' => 'Icoon',
                    'placeholder' => 'arrow-right',
                    'comment' => 'Voeg een <a href="https://fontawesome.com/search" target="_blank">FontAwesome</a> icoon toe',
                    'commentHtml' => true,
                    'type' => 'text',
                ],
                'viewBag[description]' => [
                    'tab' => 'rainlab.pages::lang.menuitem.display_tab',
                    'label' => 'Omschrijving',
                    'type' => 'text',
                ],
            ]);
        });

        Event::listen('backend.menu.extendItems', function ($navigationManager) {
            $user = BackendAuth::getUser();

            if ($user->role?->code === 'webmaster') {
                $navigationManager->removeMainMenuItem('October.System', 'system', 'settings');
            }
        });

        // Redirect Class
        $this->app[Kernel::class]
            ->pushMiddleware(BackendRedirectMiddleware::class);

        // create base preferences for new users.
        User::extend(static function ($model) {
            $model->bindEvent('model.afterCreate', function () use ($model) {
                if ($model instanceof User) {

                    UserPreference::extend(static function ($userPreference) {
                        if ($userPreference instanceof UserPreference) {
                            $userPreference->fillable([
                                'user_id',
                                'namespace',
                                'group',
                                'item',
                                'value',
                            ]);
                        }
                    });

                    UserPreference::firstOrCreate([
                        'user_id' => $model->id,
                        'namespace' => 'backend',
                        'group' => 'backend',
                        'item' => 'preferences',
                    ], [
                        'value' => [
                            'locale' => 'nl',
                            'fallback_locale' => 'en',
                            'timezone' => 'Europe/Amsterdam',
                            'editor_theme' => 'twilight',
                            'editor_word_wrap' => 'off',
                            'editor_font_size' => 12,
                            'editor_tab_size' => 4,
                            'editor_code_folding' => 'manual',
                            'editor_autocompletion' => 'manual',
                            'editor_use_emmet' => true,
                            'editor_show_gutter' => true,
                            'editor_highlight_active_line' => true,
                            'editor_auto_closing' => true,
                            'editor_use_hard_tabs' => false,
                            'editor_display_indent_guides' => false,
                            'editor_show_invisibles' => false,
                            'editor_show_print_margin' => false,
                        ],
                    ]);

                    UserPreference::extend(static function ($userPreference) {
                        if ($userPreference instanceof UserPreference) {
                            $userPreference->fillable([]);
                        }
                    });
                }
            });
        });

        Box::extend(function ($model) {
            $model->addDynamicMethod('listAllForms', function ($value) {
                return Form::get()->lists('name', 'code');
            });

            $model->addDynamicMethod('getBgColorList', function ($value) {
                $colorList = ['transparent', '#ffffff', '#f3f4f6', '#d1d5db', '#6b7280', '#374151', '#18181b', '#000000'];
                $design_settings = SiteAdminSettings::get('color_list');
                $design_settings_old = SiteAdminSettings::get('design');

                if ($design_settings) {
                    return $design_settings;
                }
                if ($design_settings_old && array_key_exists('color_list', $design_settings_old)) {
                    return $design_settings['color_list'];
                }

                return $colorList;
            });
        });

        CookiesSettings::extend(function ($model) {
            $model->settingsFields = '~/plugins/invato/settings/assets/configs/cookiefields.yaml';
        });

        $pluginManager = PluginManager::instance();

        if ($pluginManager->exists('Rainlab.User')) {
            // Add authentication fields to BoxesPage
            Event::listen('backend.form.extendFields', function ($widget) {
                if (! $widget->getController() instanceof EditorController) {
                    return;
                }

                if (! $widget->model instanceof Page) {
                    return;
                }

                $widget->addFields([
                    'custom_config[restricted_page]' => [
                        'label' => 'Pagina afschermen',
                        'comment' => 'Scherm deze pagina af zodat er alleen <a class="text-primary" href="/backend/rainlab/user/users" target="_blank"><b>ingelogde gebruikers</b></a> deze kunnen bekijken.',
                        'commentHtml' => true,
                        'type' => 'checkbox',
                        'tab' => 'CMS',
                        'order' => '200',
                        'permission' => 'rainlab.user.access_users',
                    ],
                    'custom_config[restricted_page_redirect]' => [
                        'label' => 'Niet-ingelogde bezoekers doorsturen naar:',
                        'type' => 'pagefinder',
                        'tab' => 'CMS',
                        'order' => '210',
                        'permission' => 'rainlab.user.access_users',
                        'trigger' => [
                            'field' => 'custom_config[restricted_page]',
                            'action' => 'show',
                            'condition' => 'checked',
                        ],
                    ],
                    '_restriced_page_ruler' => [
                        'type' => 'ruler',
                        'order' => '220',
                        'permission' => 'rainlab.user.access_users',
                        'tab' => 'CMS',
                        'trigger' => [
                            'field' => 'custom_config[restricted_page]',
                            'action' => 'show',
                            'condition' => 'checked',
                        ],
                    ],
                ], 'primary');
            });

            Event::listen('cms.page.beforeDisplay', function (Controller $controller, string $url, ?\Cms\Classes\Page $page = null) {
                if (! $page) {
                    return;
                }

                // Ignore Editor.
                if (isset($page->apiBag[CmsPageParams::BOXES_IS_EDITOR])) {
                    return;
                }

                // If a boxes page is available, fetch it.
                if (isset($page->apiBag[CmsPageParams::BOXES_PAGE_ID])) {
                    $boxesPage = Page::withoutGlobalScopes()->find($page->apiBag[CmsPageParams::BOXES_PAGE_ID]);

                    $user = Auth::user(); // Get the authenticated user
                    $restricedPage = 0;

                    if ($boxesPage->custom_config) {
                        if (array_key_exists('restricted_page', $boxesPage->custom_config)) {
                            $restricedPage = $boxesPage->custom_config['restricted_page'];
                        }

                        // Check if the page is restricted and user is not logged in or not activated
                        if ($restricedPage && (! $user || ! $user->activated_at)) {
                            $redirect = PageManager::resolve($boxesPage->custom_config['restricted_page_redirect']);

                            // Not allowed, so redirect to...
                            return Redirect::to($redirect->url);
                        }
                    }
                }
            });
        }

        Event::listen('backend.form.extendFields', function ($widget) {
            if (! $widget->getController() instanceof EditorController) {
                return;
            }

            if (! $widget->model instanceof Page) {
                return;
            }

            $widget->removeField('images');

            $widget->addFields([
                'custom_config[thumbnail]' => [
                    'label' => 'Afbeelding',
                    'type' => 'mediafinder',
                    'mode' => 'image',
                    'tab' => 'CMS',
                    'comment' => "Deze afbeelding kan worden gebruikt voor overzichtpagina's"
                ],
                'custom_config[excerpt]' => [
                    'label' => 'Introductietekst',
                    'type' => 'textarea',
                    'size' => 'small',
                    'tab' => 'CMS',
                    'comment' => "Deze tekst kan worden gebruikt voor overzichtpagina's"
                ],
            ], 'primary');
        });

        \Backend\FormWidgets\RichEditor::extend(function($controller) {
            $controller->addJs('/plugins/invato/settings/assets/js/richeditorButton.js');
        });
    }
}
