<?php namespace Invato\Airconditioners\Controllers;

use Backend;
use BackendMenu;
use Backend\Classes\Controller;
use Backend\Widgets\ListStructure;
use Invato\Airconditioners\Models\Airco;
use October\Rain\Support\Facades\Str;
use Flash;
use RuntimeException;

class Aircos extends Controller
{
    public $implement = [
        \Backend\Behaviors\FormController::class,
        \Backend\Behaviors\ListController::class
    ];

    public $formConfig = 'config_form.yaml';
    public $listConfig = 'config_list.yaml';

    public $requiredPermissions = [
        'invato.airconditioners.manage_plugin'
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Airconditioners', 'main-menu-item');
    }

    public function onDuplicate()
    {
        if (! $id = post('id')) {
            throw new RuntimeException('ID not specified');
        }

        $airco = Airco::find($id);
        if (! $airco) {
            throw new RuntimeException('Product not found');
        }

        $newAirco = $airco->replicate();
        $newAirco->title = $airco->title;
        $newAirco->slug = $airco->slug.'-'.Str::random(5);
        $newAirco->save();

        Flash::success(trans('invato.airconditioners::lang.airco.duplicate_success'));

        return $this->listRefresh();
    }

    public function onRestore()
    {
        if (! $id = post('id')) {
            throw new RuntimeException('ID not specified');
        }

        $model = Airco::withTrashed()->find($id);
        if (! $model) {
            throw new RuntimeException('Product not found');
        }

        if ($model->trashed()) {
            $this->restore($model->id);
            Flash::success(trans('invato.airconditioners::lang.airco.restore_success'));
        } else {
            Flash::error(trans('invato.airconditioners::lang.airco.restore_failed'));
        }

        return $this->listRefresh();
    }

    public function onRestoreSelected()
    {
        $checked = post('checked');
        if ($checked) {
            foreach ($checked as $id) {
                $this->restore($id);
            }

            Flash::success(trans('invato.airconditioners::lang.airco.restored_success'));

            return $this->listRefresh();
        }

        return $this->listRefresh();
    }

    private function restore($id)
    {
        $model = Airco::withTrashed()->find($id);
        if (! $model) {
            throw new RuntimeException('Product not found');
        }

        if ($model->trashed()) {
            $model->deleted_at = null;
            $model->save();
        }
    }

    public function listExtendColumns($list): void
    {
        if ($list instanceof ListStructure) {
            $activeFilters = $this->getActiveFilters();
            if (isset($activeFilters['with_trashed']) && $activeFilters['with_trashed'] === '1') {
                $list->addColumns([
                    'deleted_at' => [
                        'label' => trans('invato.airconditioners::lang.global.deleted_at'),
                        'type' => 'datetime',
                    ],
                ]);
            }
        }
    }

    public function getActiveFilters(): array
    {
        $filterWidget = $this->listGetFilterWidget();

        if ($filterWidget) {
            $scopes = $filterWidget->getScopes();

            $activeFilters = [];
            foreach ($scopes as $scopeName => $scope) {
                if ($scope->value) {
                    $activeFilters[$scopeName] = $scope->value;
                }
            }

            return $activeFilters;
        }

        return [];
    }

}
