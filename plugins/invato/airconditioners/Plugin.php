<?php namespace Invato\Airconditioners;

use Invato\Airconditioners\Components\AircoDetail;
use Invato\Airconditioners\Components\AircoList;
use Invato\Airconditioners\Models\AircoSettings;
use System\Classes\PluginBase;

class Plugin extends PluginBase
{
    public function boot(): void {}

    public function register(): void {}

    public function registerComponents(): array
    {
        return [
            AircoDetail::class => 'AircoDetail',
            AircoList::class => 'AircoList',
        ];
    }

    public function registerSettings(): array
    {
        return [
            'settings' => [
                'label' => trans('invato.airconditioners::lang.settings.label'),
                'description' => trans('invato.airconditioners::lang.settings.description'),
                'category' => 'Plugins',
                'icon' => 'ph ph-wind',
                'class' => AircoSettings::class,
            ],
        ];
    }
}
