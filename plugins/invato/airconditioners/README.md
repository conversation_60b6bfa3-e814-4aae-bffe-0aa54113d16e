# Plugin: Airconditioners



## Doel:

Het be<PERSON><PERSON><PERSON> van de benodigde kW voor een ruimte, met als uitkomst een aantal airco modellen.



## Backend:

De plugin heeft 2 backend pagina's: 'Airconditioners' en 'Instellingen'



### Airconditioners instellingen pagina's:

*  #### Airconditioners

Hier kunnen de airco's opgevoerd worden. Belangrijk en verplicht voor de airco's is het veld 'capaciteit'. Hierop baseert de calculator de uitkomst.

*  #### Instellingen

Hier staan de opties voor de plugin, zoals de pagina die als overzicht, formulier of detailpagina worden gebruikt. Tevens kan hier het offerte formulier ingesteld worden.



### Wie heeft toegang?

* Invato

* Klant met toegang tot de plugin.





## Front-end:

Deze plugin bevat 2 boxes: 'Airco calculator' en 'Airco calculator resultaat'. Bij de eerste box kun je het calculator formulier tonen, vervolgens dient op een andere pagina (bijvoorbeeld /aircos/resultaat) de tweede box staan. Deze heeft als fall-back (als er geen formulier is ingevuld), nog een keer het formulier in de box. Als er wel een formulier is verstuurd, worden hier de airco's getoond.



### Wie heeft toegang?

* Invato

* Klant met toegang tot de boxes en plugin.



## Plugin uitbereiden (Extensie plugin)



De catalogus plugin is met een extensie plugin gemakkelijk uit te breiden met nieuwe functies, componenten en extra velden.



### Extra velden toevoegen



Om extra velden aan te maken, is er een speciaal JSON veld in de database aangemaakt, zodat er geen migrations uitgevoerd hoeven te worden. Dit is het 'custom_options' veld.



In de Plugin.php van de extensie plugin moet in de boot() method het 'extendFields' Event aangeroepen worden om de velden toe te voegen:



```php

Event::listen('backend.form.extendFields', function ($widget) {

	if (
		!$widget->model instanceof \Invato\Airconditioners\Models\Airco
	) {
		return;
	}


	if ($widget->isNested) {

		return;

	}

	$widget->addFields([
		'custom_options[item_no]' => [
			'label' => 'Artikelnummer',
		],
	]);

	$widget->addTabFields([
		'custom_options[in_stock]' => [
			'label' => 'Op voorraad',
			'type' => 'switch',
			'tab' => 'invato.catalog::lang.tabs.description',
		],
	]);
});

```



Meer info is over het toevoegen van velden is [hier te vinden](https://docs.octobercms.com/3.x/extend/extending.html#extending-backend-forms)



Zodra de velden aangemaakt zijn en ingevuld zijn, zijn de waardes in de front-end op de volgende manier op te halen:



```
{% for item in products %}
	{{ item.custom_options.in_stock }}
{% endfor %}
```

of in de detailpagina:
```
{{ product.custom_options.item_no }}
```

