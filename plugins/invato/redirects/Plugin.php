<?php

namespace Invato\Redirects;

use Cms\Classes\CmsController;
use Invato\Redirects\Classes\RedirectMiddleware;
use System\Classes\PluginBase;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{
    /**
     * register method, called when the plugin is first registered.
     */
    public function register()
    {
    }

    /**
     * boot method, called right before the request route.
     */
    public function boot()
    {
        // Redirect middleware
        CmsController::extend(static function ($controller) {
            $controller->middleware(RedirectMiddleware::class);
        });
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents()
    {
    }

    /**
     * registerSettings used by the backend.
     */
    public function registerSettings()
    {
    }
}
