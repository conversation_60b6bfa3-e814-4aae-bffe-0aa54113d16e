<?php

namespace Invato\Redirects\Models;

use Model;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Validation;

/**
 * Model
 */
class Redirect extends Model
{
    use Validation;
    use SoftDelete;

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_redirects_redirects';

    /**
     * @var array rules for validation.
     */
    public $rules = [];

    /**
     * @var array columns to guard
     */
    protected $guarded = [];

    protected $casts = [
        'id' => 'integer',
        'new_url' => 'string',
        'old_url' => 'string',
        'status' => 'integer',
        'comment' => 'string',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
        'deleted_at' => 'timestamp',
    ];
}
