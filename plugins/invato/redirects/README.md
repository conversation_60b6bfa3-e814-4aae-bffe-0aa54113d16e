# Automatische Redirects: Uitleg en Gebruiksinstructies

Met de `CanRedirectModelTrait` kun je eenvoudig automatische redirects beheren voor modellen die gekoppeld zijn aan
detailpagina's. Dit document legt uit hoe de functionaliteit werkt en hoe je het kunt inzetten voor modellen in jouw
applicatie.

## Voorwaarden

Om gebruik te maken van automatische redirects, moeten de volgende zaken geconfigureerd zijn:

1. **Trait**: Voeg de `CanRedirectModelTrait` toe aan het model dat je wilt ondersteunen.
2. **Slug en detailpagina**: Zorg ervoor dat je model een unieke slug heeft en dat deze is gekoppeld aan een
   detailpagina via een controller.
3. **Redirect Model**: Implementeer de `Redirect`-logica, zoals beschreven in de `CanRedirectModelTrait` (zie de
   `Invato\Redirects\Models\Redirect`).

---

## Functionaliteit

De `CanRedirectModelTrait` biedt twee primaire functionaliteiten:

1. **Automatisch aanmaken van een redirect** wanneer een model wordt verwijderd.
2. **Verwijderen van de redirect** wanneer een model wordt hersteld.

---

## Hoe werkt het?

### 1. Aanmaken van een Redirect (bij verwijderen van een model)

Wanneer een model wordt verwijderd, wordt automatisch een redirect aangemaakt. Hierbij wordt de oude URL gegenereerd aan
de hand van de slug van het model en de detailpagina waarmee het is gekoppeld. Deze functionaliteit is beschikbaar
dankzij de methode `createRedirect`.

#### Syntax van `createRedirect`

```php
static::createRedirect(
    plugin: 'jouw_plugin',
    modelRecord: $jouwModel,
    detailPageController: DetailPageController::class,
    status: 301
);
```

**Uitleg parameters:**

- `plugin`: De naam van je plugin, bijv. `catalog`.
- `modelRecord`: De instantie van het model waarvoor de redirect wordt gemaakt.
- `detailPageController`: De controller gekoppeld aan de detailpagina.
- `status`: De HTTP-statuscode (meestal `301` voor permanente redirects).

Als een bijbehorende detailpagina-component wordt gevonden, wordt een redirect aangemaakt van de oude URL naar `/`.

**Voorbeeld in model:**

```php
protected static function booted(): void
{
    static::deleting(static function ($category) {
        static::createRedirect(
            plugin: 'catalog',
            modelRecord: $category,
            detailPageController: CategoryDetail::class,
            status: 301
        );
    });
}
```

---

### 2. Verwijderen van een Redirect (bij herstel van een model)

Wanneer een model wordt hersteld (soft delete), wordt de bijbehorende redirect automatisch verwijderd dankzij de methode
`deleteRedirect`.

#### Syntax van `deleteRedirect`

```php
static::deleteRedirect($modelRecord);
```

**Voorbeeld in model:**

```php
protected static function booted(): void
{
    static::restored(static function ($category) {
        static::deleteRedirect($category);
    });
}
```

---

## Belangrijke configuraties in het model

Voor het gebruik van automatische redirects moet je model worden geconfigureerd met:

- **`CanRedirectModelTrait`**: Voor het inschakelen van redirectfunctionaliteit.

- **Slug**: Voeg een unieke `slug`-kolom toe aan je model, en configureer deze met bijvoorbeeld
  `October\Rain\Database\Traits\Sluggable`.

Hier is een voorbeeld van een volledig model:

```php
use Invato\Redirects\traits\CanRedirectModelTrait;

class Category extends Model
{
    use CanRedirectModelTrait;

    protected $slugs = [
        'slug' => 'title',
    ];
}
```

---

## Gebruik in de praktijk

### 1. Redirects maken

Stel dat een categorie met de slug `example-category` is verwijderd. De `createRedirect`-methode genereert automatisch
een redirect van `/category/example-category` naar `/`.

### 2. Redirects verwijderen

Wanneer diezelfde categorie wordt hersteld, wordt de eerder aangemaakte redirect automatisch verwijderd.

---

## Veelgestelde vragen

### Wat gebeurt er als er geen detailpagina wordt gevonden?

Als geen geldige detailpagina-component kan worden gevonden, slaat de `createRedirect`-methode de actie over. Er wordt
een melding weergegeven via de Flash-berichtgeving van October CMS.

### Kan ik de functionaliteit aanpassen?

Ja, je kunt zowel `createRedirect` als `deleteRedirect` overschrijven in een aangepaste trait of in je model als je
specifieke logica wilt implementeren.

---

Met deze handleiding kun je eenvoudig automatische redirects gebruiken in je project. Mocht je problemen ondervinden,
raadpleeg dan de logs of controleer of alle benodigde configuraties correct zijn ingesteld.
