<?php

namespace Invato\Redirects\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;

class Redirects extends Controller
{
    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public $importExportConfig = 'config_import_export.yaml';

    public $requiredPermissions = [
        'invato.redirects.manage_redirects',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Redirects', 'main-menu-item');
    }
}
