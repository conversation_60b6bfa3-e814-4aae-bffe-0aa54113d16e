<?php

namespace Invato\Blog\Components;

use Cms\Classes\ComponentBase;
use Cms\Classes\Page;
use Illuminate\Pagination\LengthAwarePaginator;
use Invato\Blog\Models\BlogSetting;
use Invato\Blog\Models\Category;
use Invato\Blog\Models\Post;
use October\Rain\Database\Collection;

class PostList extends ComponentBase
{
    /**
     * A collection of records to display
     *
     * @var Collection
     */
    public $posts;

    public $category;

    public $blogPage;

    public $postPage;

    public $categoryPage;

    public $showAuthor;

    public $showDates;

    public $hidePost;

    public function componentDetails()
    {
        return [
            'name' => 'invato.blog::lang.postlist.name',
            'description' => 'invato.blog::lang.postlist.description',
        ];
    }

    /**
     * @link https://docs.octobercms.com/3.x/element/inspector-types.html
     */
    public function defineProperties()
    {
        return [
            'maxItems' => [
                'title' => 'Max Items',
                'type' => 'string',
                'placeholder' => 'Leave empty for all posts',
            ],
            'itemsPerPage' => [
                'title' => 'Items per page',
                'type' => 'string',
                'placeholder' => 'Leave empty if max items is filled',
            ],
            'sorting' => [
                'title' => 'Sort By',
                'type' => 'dropdown',
                'options' => [
                    'publication_date' => 'Publication Date',
                    'title' => 'Title Alphabetical',
                ],
            ],
            'sortDirection' => [
                'title' => 'Sort direction',
                'type' => 'dropdown',
                'options' => [
                    'asc' => 'Ascending',
                    'desc' => 'Descending',
                ],
                'default' => 'desc',
            ],
            'category' => [
                'title' => 'Category',
                'type' => 'dropdown',
                'comment' => 'List posts from this category',
            ],
            'hidePost' => [
                'title' => 'invato.blog::lang.settings.hidecurrentpost',
                'description' => 'Enter post slug',
                'default' => '{{ :slug }}',
                'type' => 'string',
            ],
        ];
    }

    //
    // Rendering and processing
    //
    public function onRun()
    {
        $this->blogPage = BlogSetting::get('blogPage');
        $this->postPage = BlogSetting::get('postPage');
        $this->categoryPage = BlogSetting::get('categoryPage');
        $this->showAuthor = BlogSetting::get('showAuthor');
        $this->showDates = BlogSetting::get('showDates');
        $this->hidePost = $this->property('hidePost');

        if ($this->property('category')) {
            $this->posts = $this->getCategoryPosts();
        } else {
            $this->posts = $this->getPosts();
        }
    }

    public function getPosts()
    {
        $query = Post::withoutTrashed()
            ->published();

        if ($this->property('isFeaturedFiltered')) {
            $query->where('is_featured', '=', 0);
        }

        return $this->filterPosts($query);
    }

    public function getFeaturedPost()
    {
        $currentPage = (int) request()?->input('posts', 1);

        if ($currentPage === 1) {
            return Post::withoutTrashed()
                ->published()
                ->where('is_featured', '=', 1)
                ->first();
        }

        return null;
    }

    public function getCategoryPosts()
    {
        $categoryQuery = Category::query();
        $categoryQuery->where('slug', $this->property('category'));
        $category = $categoryQuery->first();

        $postQuery = $category->posts()->published();

        return $this->filterPosts($postQuery);
    }

    private function filterPosts($query)
    {
        if ($this->property('hidePost')) {
            $query->where('slug', '!=', $this->property('hidePost'));
        }

        if ($this->property('sorting')) {
            $query->orderBy(
                $this->property('sorting'),
                $this->property('sortDirection') ?: 'desc'
            );
        }

        if ($this->property('maxItems')) {
            $query->take($this->property('maxItems'));
        }

        if ($this->property('sorting')) {
            $query->orderBy(
                $this->property('sorting'),
                $this->property('sortDirection') ?: 'desc'
            );
        }

        $itemsPerPage = $this->property('itemsPerPage');
        $firstPageLimit = $this->property('firstPageLimit');

        if (
            isset($itemsPerPage, $firstPageLimit)
            &&
            (is_numeric($itemsPerPage) && is_numeric($firstPageLimit))
        ) {
            $total = $query->count();

            $currentPage = (int) request()->input('posts', 1);

            if (
                ($currentPage === 1 && $firstPageLimit)
                &&
                $this->getFeaturedPost()
            ) {
                $data = $query->take($firstPageLimit)->get();
            } else {
                $offset = $firstPageLimit + (($currentPage - 2) * $itemsPerPage);
                $data = $query->skip($offset)->take($itemsPerPage)->get();
            }

            return (new LengthAwarePaginator(
                $data,
                $total,
                $itemsPerPage,
                $currentPage,
                [
                    'path' => request()->url(),
                    'query' => request()->query(),
                ]
            ))->setPageName('posts');
        }

        return $query->get();
    }

    public function getCategoryPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }

    public function getPostPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }
}
