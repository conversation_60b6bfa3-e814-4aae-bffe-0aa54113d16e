<?php

namespace Invato\SiteConfiguration\Controllers;

use Backend\Classes\Controller;
use BackendMenu;
use File;
use Markdown;

class Readme extends Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->pageTitle = 'Readme';
        BackendMenu::setContext('Invato.SiteConfiguration', 'siteconfiguration', 'readme');
    }

    public function index()
    {
        $markdownPath = plugins_path('invato/siteconfiguration/README.md');

        if (File::exists($markdownPath)) {
            $markdownContent = File::get($markdownPath);
            $this->vars['content'] = Markdown::parse($markdownContent);
        } else {
            $this->vars['content'] = '<p>README.md not found.</p>';
        }
    }
}
