<?php

namespace Invato\SiteConfiguration\Controllers;

use Backend\Classes\Controller;
use BackendMenu;
use File;
use Markdown;

class Manual extends Controller
{
    public function __construct()
    {
        parent::__construct();
        
        $this->pageTitle = 'Manual';
        BackendMenu::setContext('Invato.SiteConfiguration', 'siteconfiguration', 'manual');
    }

    public function index()
    {
        $markdownPath = plugins_path('invato/siteconfiguration/MANUAL.md');
        
        if (File::exists($markdownPath)) {
            $markdownContent = File::get($markdownPath);
            $this->vars['content'] = Markdown::parse($markdownContent);
        } else {
            $this->vars['content'] = '<p>MANUAL.md not found.</p>';
        }
    }
}
