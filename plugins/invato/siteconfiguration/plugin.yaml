plugin:
    name: 'invato.siteconfiguration::lang.plugin.name'
    description: 'invato.siteconfiguration::lang.plugin.description'
    author: Invato
    icon: oc-icon-cog
    homepage: ''
permissions:
    'invato.siteconfiguration.manage_plugin':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_plugin'
    'invato.siteconfiguration.manage_admin':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_admin'
    'invato.siteconfiguration.manage_config':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_config'
    'invato.siteconfiguration.manage_theme':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_theme'
navigation:
    siteconfiguration:
        label: 'invato.siteconfiguration::lang.plugin.name'
        url: /
        icon: icon-cog
        iconSvg: plugins/invato/siteconfiguration/assets/images/invato-siteconfig.svg
        permissions:
            - 'invato.siteconfiguration.manage_plugin'
        sideMenu:
            content-section:
                label: 'Content'
                itemType: section

            settings:
                label: 'invato.siteconfiguration::lang.navigation.companysettings'
                url: system/settings/update/invato/siteconfiguration/settings
                icon: icon-building
                permissions:
                    - 'invato.siteconfiguration.manage_config'
            adminsettings:
                label: 'invato.siteconfiguration::lang.navigation.adminsettings'
                url: system/settings/update/invato/siteconfiguration/adminsettings
                icon: icon-user-secret
                permissions:
                    - 'invato.siteconfiguration.manage_admin'
            themesettings:
                label: 'invato.siteconfiguration::lang.navigation.themesettings'
                url: system/settings/update/invato/siteconfiguration/themesettings
                icon: icon-paint-brush
                permissions:
                    - 'invato.siteconfiguration.manage_theme'

            documentation-section:
                label: 'Documentation'
                itemType: section

            readme:
                label: 'Readme'
                url: invato/siteconfiguration/readme
                icon: icon-book
            manual:
                label: 'Manual'
                url: invato/siteconfiguration/manual
                icon: icon-book
