plugin:
    name: 'invato.siteconfiguration::lang.plugin.name'
    description: 'invato.siteconfiguration::lang.plugin.description'
    author: Invato
    icon: icon-id-card-1
    homepage: ''
permissions:
    invato.siteconfiguration.manage_admin:
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.settings.label'
navigation:
    siteconfiguration:
        label: 'invato.siteconfiguration::lang.plugin.name'
        url: /
        order: 800
        icon: icon-id-card-1
        iconSvg: plugins/invato/siteconfiguration/assets/images/invato-siteconfig.svg
        sideMenu:
            siteconfiguration-settings-section:
                label: 'Settings'
                itemType: section

            settings:
                label: 'invato.siteconfiguration::lang.navigation.companysettings'
                url: system/settings/update/invato/siteconfiguration/settings
                icon: icon-id-card-1
            themesettings:
                label: 'invato.siteconfiguration::lang.navigation.themesettings'
                url: system/settings/update/invato/siteconfiguration/themesettings
                icon: icon-text-format-ul
            adminsettings:
                label: 'invato.siteconfiguration::lang.navigation.adminsettings'
                url: system/settings/update/invato/siteconfiguration/adminsettings
                icon: icon-cog
                permissions:
                    - invato.siteconfiguration.manage_admin

            siteconfiguration-documentation-section:
                label: 'Documentation'
                itemType: section

            siteconfiguration-readme:
                label: 'Readme'
                url: invato/siteconfiguration/readme
                icon: icon-book
                permissions:
                    - 'superusers.view_readme'
            siteconfiguration-manual:
                label: 'Manual'
                url: invato/siteconfiguration/manual
                icon: icon-book
                permissions:
                    - 'invato.catalog.manage_plugin'