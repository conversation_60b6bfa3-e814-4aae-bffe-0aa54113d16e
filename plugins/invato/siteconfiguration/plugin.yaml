plugin:
    name: 'invato.siteconfiguration::lang.plugin.name'
    description: 'invato.siteconfiguration::lang.plugin.description'
    author: Invato
    icon: oc-icon-cog
    homepage: ''
permissions:
    'invato.siteconfiguration.manage_plugin':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_plugin'
    'invato.siteconfiguration.manage_admin':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_admin'
    'invato.siteconfiguration.manage_config':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_config'
    'invato.siteconfiguration.manage_theme':
        tab: 'invato.siteconfiguration::lang.plugin.name'
        label: 'invato.siteconfiguration::lang.permissions.manage_theme'
navigation:
    siteconfiguration:
        label: 'Site Configuration'
        url: /
        icon: icon-cog
        sideMenu:
            readme:
                label: 'Readme'
                url: invato/siteconfiguration/readme
                icon: icon-book
            manual:
                label: 'Manual'
                url: invato/siteconfiguration/manual
                icon: icon-book
