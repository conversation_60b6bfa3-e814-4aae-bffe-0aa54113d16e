# Site Configuration Plugin

## Overzicht

De Invato Site Configuration plugin biedt een uitgebreide oplossing voor het beheren van site-configuratie binnen uw OctoberCMS website. Met deze plugin kunt u eenvoudig bedrijfsgegevens, admin instellingen en theme instellingen beheren.

## Installatie

1. Plaats de plugin in de `plugins/invato/siteconfiguration` directory
2. Voer `php artisan october:migrate` uit om de database bij te werken
3. De plugin is nu klaar voor gebruik

## Functionaliteiten

### Settings Models

De plugin bevat drie hoofdsettings models:

#### SiteConfigSettings
- Beheer van bedrijfsgegevens
- Contactinformatie
- Logo's en media
- SEO instellingen
- Openingstijden
- Sociale media links

#### SiteAdminSettings  
- Admin configuratie
- Boxes configuratie
- Layout instellingen
- Kleur instellingen

#### SiteThemeSettings
- Theme configuratie
- <PERSON>ie instellingen
- Maintenance instellingen
- Frontend opties

### Permissions

De plugin definieert de volgende permissions:

- `invato.siteconfiguration.manage_plugin` - Algemeen beheer van de plugin
- `invato.siteconfiguration.manage_admin` - Beheer admin instellingen
- `invato.siteconfiguration.manage_config` - Beheer bedrijfsgegevens
- `invato.siteconfiguration.manage_theme` - Beheer theme instellingen

### Menu Structuur

De plugin voegt een hoofdmenu item toe met de volgende submenu's:

#### Content
- Bedrijfsgegevens
- Admin instellingen  
- Site instellingen

#### Settings
- General (algemene instellingen)

#### Documentation
- Readme (deze documentatie)
- Manual (gebruikershandleiding)

## Technische Details

### Components

#### SiteConfig Component
De plugin bevat een `SiteConfig` component die gebruikt kan worden om configuratie-instellingen beschikbaar te maken in de frontend.

### Integration met andere plugins

De plugin integreert met:
- **OFFLINE Boxes** - Voor het filteren van beschikbare partials en layouts
- **Backend theming** - Voor kleurpaletten en styling

### Settings Menu Context

De plugin gebruikt de `SettingsMenuContextTrait` om ervoor te zorgen dat de juiste menu context wordt ingesteld wanneer gebruikers navigeren naar de verschillende settings pagina's.

## Configuratie

Na installatie zijn de instellingen beschikbaar via:
- **Backend** > **Instellingen** > **Site Configuratie**
- Of via het hoofdmenu **Site Configuratie**

## Ontwikkeling

### Extending Settings

Om extra velden toe te voegen aan de settings models, gebruik het `backend.form.extendFields` event:

```php
Event::listen('backend.form.extendFields', function ($widget) {
    if (!$widget->model instanceof \Invato\SiteConfiguration\Models\SiteConfigSettings) {
        return;
    }
    
    $widget->addFields([
        'custom_field' => [
            'label' => 'Custom Field',
            'type' => 'text',
        ],
    ]);
});
```

### View Sharing

De plugin deelt automatisch de volgende variabelen met alle views:
- `$appUrl` - Secure URL van de applicatie
- `$companyName` - Bedrijfsnaam uit de instellingen
- `$companyData` - Volledige bedrijfsgegevens
- `$companyLogo` - URL naar het bedrijfslogo

## Gebruikte Permissions

- `invato.siteconfiguration.manage_plugin`
- `invato.siteconfiguration.manage_admin`
- `invato.siteconfiguration.manage_config`
- `invato.siteconfiguration.manage_theme`
- `superusers.view_readme`
