# Handleiding Site Configuratie

## Introductie

De Invato Site Configuration plugin biedt een uitgebreide oplossing voor het beheren van alle site-configuratie binnen uw OctoberCMS website. Met deze plugin kunt u eenvoudig bedrijfsgegevens, admin instellingen en theme instellingen beheren vanuit één centrale locatie.

## Toegang tot de instellingen

Na installatie kunt u de plugin configureren via het backend:

1. Ga naar **Site Configuratie** in het hoofdmenu
2. Of ga naar **Instellingen** > **Site Configuratie** > **Bedrijfsgegevens/Admin instellingen/Site instellingen**

## Functionaliteiten

### Bedrijfsgegevens beheren

Via **Site Configuratie** > **Bedrijfsgegevens** kunt u de volgende informatie beheren:

#### Algemene bedrijfsgegevens
- **Bedrijfsnaam**: De officiële naam van uw bedrijf
- **Adresgegevens**: Straat, huisnummer, postcode, plaats en land
- **Contactgegevens**: Telefoon, mobiel en e-mailadres
- **Bedrijfsregistratie**: KvK nummer en BTW nummer
- **Google Maps**: Embed code voor locatie weergave

#### Extra adressen
- Voeg meerdere vestigingen of adressen toe
- Elk adres kan een eigen label en contactgegevens hebben
- Koppel adressen aan specifieke pagina's

#### Bankrekeningen
- Beheer meerdere bankrekeningen
- Ondersteuning voor IBAN en BIC
- Tenaamstelling per rekening

#### Logo's en media
- **Primair logo**: Hoofdlogo voor de website
- **E-mail logo**: Specifiek logo voor e-mailcommunicatie
- **Favicon**: Website icoon

#### Openingstijden
- Stel openingstijden in per dag van de week
- Ondersteuning voor meerdere tijdslots per dag
- Speciale openingstijden voor feestdagen

#### Sociale media
- Links naar sociale media profielen
- Ondersteuning voor alle populaire platforms

#### SEO instellingen
- Meta titel suffix
- Aanvullende meta tags
- Globale SEO configuratie

### Admin instellingen beheren

Via **Site Configuratie** > **Admin instellingen** kunt u:

#### Boxes configuratie
- Selecteer welke Boxes partials beschikbaar zijn
- Filter beschikbare layouts
- Configureer admin interface opties

#### Kleurinstellingen
- Beheer kleurpaletten
- Configureer admin interface kleuren

#### Footer instellingen
- Developer link configuratie
- Footer opties

### Site instellingen beheren

Via **Site Configuratie** > **Site instellingen** kunt u:

#### Cookie instellingen
- Cookie melding tekst
- Accept/reject button teksten
- Privacy policy link configuratie

#### Maintenance modus
- Status code configuratie
- Maintenance pagina instellingen

#### Theme opties
- Frontend configuratie
- Theme specifieke instellingen

## Frontend integratie

### SiteConfig Component

De plugin biedt een `SiteConfig` component die u kunt gebruiken op uw frontend pagina's:

1. Voeg het component toe aan uw pagina
2. Gebruik de beschikbare variabelen in uw templates:
   - `{{ companyName }}` - Bedrijfsnaam
   - `{{ companyData }}` - Volledige bedrijfsgegevens
   - `{{ companyLogo }}` - Logo URL

### Automatische view variabelen

De plugin deelt automatisch de volgende variabelen met alle views:
- `$appUrl` - Secure URL van de applicatie
- `$companyName` - Bedrijfsnaam
- `$companyData` - Bedrijfsgegevens object
- `$companyLogo` - URL naar het bedrijfslogo

## Permissions en toegang

De plugin gebruikt een uitgebreid permission systeem:

- **Manage Site Configuration**: Algemene toegang tot de plugin
- **Manage admin settings**: Toegang tot admin configuratie
- **Manage company information**: Toegang tot bedrijfsgegevens
- **Manage theme settings**: Toegang tot theme instellingen

Zorg ervoor dat gebruikers de juiste permissions hebben voor de functionaliteiten die zij nodig hebben.

## Tips en best practices

1. **Regelmatige backup**: Maak regelmatig een backup van uw configuratie
2. **Logo formaten**: Gebruik optimale afbeeldingsformaten voor snelle laadtijden
3. **SEO**: Vul alle SEO velden in voor betere zoekmachine optimalisatie
4. **Permissions**: Geef gebruikers alleen toegang tot de instellingen die zij nodig hebben
5. **Testing**: Test wijzigingen eerst op een staging omgeving

## Troubleshooting

### Veelvoorkomende problemen

**Logo's worden niet weergegeven**
- Controleer of de afbeeldingen correct zijn geüpload
- Verificeer de bestandspermissions
- Controleer de storage configuratie

**Instellingen worden niet opgeslagen**
- Controleer de database connectie
- Verificeer de user permissions
- Controleer de server logs voor errors

**Menu items niet zichtbaar**
- Controleer de user permissions
- Verificeer de plugin installatie
- Controleer de backend cache

Voor verdere ondersteuning, raadpleeg de README.md of neem contact op met de ontwikkelaar.
