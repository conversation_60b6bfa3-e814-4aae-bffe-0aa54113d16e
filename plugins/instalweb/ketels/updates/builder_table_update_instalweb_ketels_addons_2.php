<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInstalwebKetelsAddons2 extends Migration
{
    public function up()
    {
        Schema::table('instalweb_ketels_addons', function($table)
        {
            $table->decimal('price', 8, 2)->nullable(false)->unsigned(false)->default(0.00)->comment(null)->change();
        });
    }

    public function down()
    {
        Schema::table('instalweb_ketels_addons', function($table)
        {
            $table->integer('price')->nullable()->unsigned(false)->default(null)->comment(null)->change();
        });
    }
}
