<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsAddons extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_addons', function($table)
        {
            $table->increments('id')->unsigned();
            $table->string('title')->nullable();
            $table->integer('price')->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(1);
            $table->boolean('is_required')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_addons');
    }
}
