<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsOffers extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_offers', function($table)
        {
            $table->increments('id')->unsigned();
            $table->string('hash')->nullable();
            $table->integer('ketel_id')->nullable();
            $table->integer('thermostat_id')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('street')->nullable();
            $table->string('housenumber')->nullable();
            $table->string('zipcode')->nullable();
            $table->string('city')->nullable();
            $table->string('telephone')->nullable();
            $table->string('email')->nullable();
            $table->integer('service_contract')->nullable();
            $table->string('status')->nullable();
            $table->date('approved_date')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_offers');
    }
}
