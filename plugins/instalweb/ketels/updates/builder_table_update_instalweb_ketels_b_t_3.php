<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInstalwebKetelsBT3 extends Migration
{
    public function up()
    {
        Schema::table('instalweb_ketels_b_t', function($table)
        {
            $table->dropPrimary(['boilers_id','thermostats_id']);
            $table->integer('boiler_id')->unsigned();
            $table->integer('thermostat_id')->unsigned();
            $table->dropColumn('boilers_id');
            $table->dropColumn('thermostats_id');
            $table->primary(['boiler_id','thermostat_id']);
        });
    }
    
    public function down()
    {
        Schema::table('instalweb_ketels_b_t', function($table)
        {
            $table->dropPrimary(['boiler_id','thermostat_id']);
            $table->dropColumn('boiler_id');
            $table->dropColumn('thermostat_id');
            $table->integer('boilers_id')->unsigned();
            $table->integer('thermostats_id')->unsigned();
            $table->primary(['boilers_id','thermostats_id']);
        });
    }
}
