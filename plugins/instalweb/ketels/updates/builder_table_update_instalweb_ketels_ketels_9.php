<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInstalwebKetelsKetels9 extends Migration
{
    public function up()
    {
        Schema::table('instalweb_ketels_ketels', function($table)
        {
            $table->string('energy_label_water')->nullable();
            $table->text('short_description')->nullable()->change();
            $table->renameColumn('energy_label_small', 'energy_label_heating');
        });
    }
    
    public function down()
    {
        Schema::table('instalweb_ketels_ketels', function($table)
        {
            $table->dropColumn('energy_label_water');
            $table->text('short_description')->nullable(false)->change();
            $table->renameColumn('energy_label_heating', 'energy_label_small');
        });
    }
}
