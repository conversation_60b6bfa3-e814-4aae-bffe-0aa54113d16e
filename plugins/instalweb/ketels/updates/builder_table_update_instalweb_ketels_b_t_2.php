<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInstalwebKetelsBT2 extends Migration
{
    public function up()
    {
        Schema::table('instalweb_ketels_b_t', function($table)
        {
            $table->dropPrimary(['ketels_id','thermostats_id']);
            $table->renameColumn('ketels_id', 'boilers_id');
            $table->primary(['boilers_id','thermostats_id']);
        });
    }
    
    public function down()
    {
        Schema::table('instalweb_ketels_b_t', function($table)
        {
            $table->dropPrimary(['boilers_id','thermostats_id']);
            $table->renameColumn('boilers_id', 'ketels_id');
            $table->primary(['ketels_id','thermostats_id']);
        });
    }
}
