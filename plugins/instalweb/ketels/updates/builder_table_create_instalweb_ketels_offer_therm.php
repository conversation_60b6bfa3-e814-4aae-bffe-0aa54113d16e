<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsOfferTherm extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_offer_therm', function($table)
        {
            $table->integer('offer_id')->unsigned();
            $table->integer('thermostat_id')->unsigned();
            $table->primary(['offer_id', 'thermostat_id']);
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_offer_therm');
    }
}
