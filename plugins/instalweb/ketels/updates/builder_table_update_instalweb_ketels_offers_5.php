<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInstalwebKetelsOffers5 extends Migration
{
    public function up()
    {
        Schema::table('instalweb_ketels_offers', function($table)
        {
            $table->decimal('price', 8, 2)->nullable(false)->unsigned(false)->default(0.00)->comment(null)->change();
        });
    }

    public function down()
    {
        Schema::table('instalweb_ketels_offers', function($table)
        {
            $table->integer('price')->nullable()->unsigned(false)->default(null)->comment(null)->change();
        });
    }
}
