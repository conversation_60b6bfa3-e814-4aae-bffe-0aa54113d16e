<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsMerken extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_merken', function($table)
        {
            $table->engine = 'InnoDB';
            $table->increments('id')->unsigned();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->smallInteger('sort_order')->nullable()->default(0);
            $table->string('title');
            $table->string('slug');
            $table->text('description');
            $table->boolean('inactive')->nullable();
            $table->boolean('special')->nullable();
            $table->string('meta_title');
            $table->string('meta_description');
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_merken');
    }
}
