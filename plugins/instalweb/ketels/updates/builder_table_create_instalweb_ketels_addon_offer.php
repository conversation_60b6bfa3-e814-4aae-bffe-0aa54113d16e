<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsAddonOffer extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_addon_offer', function($table)
        {
            $table->integer('addon_id')->unsigned();
            $table->integer('offer_id')->unsigned();
            $table->primary(['addon_id', 'offer_id']);
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_addon_offer');
    }
}
