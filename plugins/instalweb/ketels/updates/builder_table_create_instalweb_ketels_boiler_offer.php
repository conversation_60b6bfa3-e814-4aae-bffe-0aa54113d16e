<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsBoilerOffer extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_boiler_offer', function($table)
        {
            $table->integer('boiler_id')->unsigned();
            $table->integer('offer_id')->unsigned();
            $table->primary(['boiler_id', 'offer_id']);
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_boiler_offer');
    }
}
