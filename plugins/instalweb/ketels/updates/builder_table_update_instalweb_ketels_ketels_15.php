<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInstalwebKetelsKetels15 extends Migration
{
    public function up()
    {
        Schema::table('instalweb_ketels_ketels', function($table)
        {
            $table->smallInteger('merken_id')->nullable()->change();
            $table->string('slug', 255)->nullable()->change();
            $table->text('description')->nullable()->change();
            $table->string('image', 255)->nullable()->change();
        });
    }
    
    public function down()
    {
        Schema::table('instalweb_ketels_ketels', function($table)
        {
            $table->smallInteger('merken_id')->nullable(false)->change();
            $table->string('slug', 255)->nullable(false)->change();
            $table->text('description')->nullable(false)->change();
            $table->string('image', 255)->nullable(false)->change();
        });
    }
}
