<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableDeleteInstalwebKetelsKetelsThermostats extends Migration
{
    public function up()
    {
        Schema::dropIfExists('instalweb_ketels_ketels_thermostats');
    }
    
    public function down()
    {
        Schema::create('instalweb_ketels_ketels_thermostats', function($table)
        {
            $table->engine = 'InnoDB';
            $table->integer('ketels_id')->unsigned();
            $table->integer('thermostats_id')->unsigned();
        });
    }
}
