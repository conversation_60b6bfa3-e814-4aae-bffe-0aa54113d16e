<?php namespace Instalweb\Ketels\Components;

use Cms\Classes\ComponentBase;
use Db;
use Instalweb\Ketels\Models\Boiler;
use Instalweb\Ketels\Models\Brand;

class BrandList extends ComponentBase
{
    /**
     * A collection of records to display
     * @var \October\Rain\Database\Collection
     */
    public $brand;
    public $brands;
    public $boilers;
    


    public function componentDetails()
    {
      return [
        'name'        => 'Merken overzicht',
        'description' => 'Merken overzichtpagina'
      ];
    }

    //
    // Properties
    //

    public function defineProperties()
    {
        return [
            'slug' => [
                'title'       => 'Merk url',
                'description' => 'Merk url',
                'default'     => '{{ :slug }}',
                'type'        => 'string',
            ],
        ];
    }
    //
    // Rendering and processing
    //

    public function onRender()
    {
        if (empty($this->brand)) {
            $this->brand = $this->getBrand();
        }
    }

    public function onRun()
    {
        $this->brand = $this->getBrand();
        $this->boilers = Boiler::where('merken_id', $this->brand->id)->orderBy('sort_order')->get();
        $this->brands = Brand::where('inactive', false)->where('type', 'boiler')->orderBy('sort_order')->get();
        
    }

    protected function getBrand()
    {
        $slug = $this->property('slug');

        $brand = new Brand;
        $query = $brand->query();

        $query->where('slug', $slug);

        $brand = $query->first();


        return $brand;
    }

}
