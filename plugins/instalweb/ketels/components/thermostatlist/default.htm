{% set brands = __SELF__.brands %}
{% set thermostats = __SELF__.thermostats %}

{% partial '@intro' brands=brands %}

<div class="bg-white">
  <div x-data="openClose">
    {% partial '@filters_mobile' brands=brands %}

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="relative z-10 flex items-baseline justify-between pt-10 lg:pt-16 pb-6 border-b border-gray-200">
            <h2 class="text-2xl lg:text-4xl font-extrabold tracking-tight text-gray-900">{{ 'Ons assortiment'|_ }}</h2>

            <div class="flex items-center">
              {# {% partial '@sorting' %} #}

              {# <button type="button" class="p-2 -m-2 ml-4 sm:ml-6 text-gray-400 hover:text-gray-500 lg:hidden" @click="toggle">
                  <span class="sr-only">{{ 'Filters'|_ }}</span>
                  <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                  </svg>
              </button> #}
            </div>
          </div>

        <section aria-labelledby="products-heading" class="pt-6 pb-24">
            <h2 id="products-heading" class="sr-only">{{ 'Thermostaten'|_ }}</h2>

            <div class="grid grid-cols-1 gap-x-8 gap-y-10">

                {# {% partial '@filters' brands=brands %} #}

                <!-- Product grid -->
                <div class="">
                  <h2 id="product-heading" class="sr-only">{{ 'Thermostaten'|_ }}</h2>
                  <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-2 sm:gap-x-6 sm:gap-y-10 lg:grid-cols-2 lg:gap-x-8">

                  {% for item in thermostats %}
                    {% if not item.merken.inactive %}
                        {% partial '@thermostat' item=item listview="true" %}
                    {% endif %}
                  {% endfor %}

                  </div>
                </div>
            </div>
        </section>
    </main>
  </div>
</div>
