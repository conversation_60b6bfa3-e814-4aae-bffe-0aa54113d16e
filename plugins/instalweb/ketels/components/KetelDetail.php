<?php namespace Instalweb\Ketels\Components;

use Cms\Classes\ComponentBase;
use Db;
use Input;
use Instalweb\Ketels\Models\Boiler;
use Instalweb\Ketels\Models\Brand;
use Instalweb\Ketels\Models\Thermostat;
use Instalweb\Ketels\Models\Addon;
use Instalweb\Ketels\Models\KetelSettings;
use Instalweb\Ketels\Models\Offer;
use Log;
use Mail;

class KetelDetail extends ComponentBase
{
    /**
     * A collection of records to display
     * @var \October\Rain\Database\Collection
     */
    public $brands;
    public $boilers;
    public $boiler;
    public $thermostat;
    public $addons;


    public function componentDetails()
    {
      return [
        'name'        => 'Ketel detail',
        'description' => 'CV Ketel detailpagina'
      ];
    }

    //
    // Properties
    //

    public function defineProperties()
    {
        return [
            'slug' => [
                'title'       => 'Ketel url',
                'description' => 'Ketel url',
                'default'     => '{{ :slug }}',
                'type'        => 'string',
            ],
        ];
    }
    //
    // Rendering and processing
    //

    public function onRun()
    {
      $this->brands = Brand::where('inactive', false)->where('type', 'boiler')->orderBy('sort_order')->get();
      $this->boilers = Boiler::orderBy('sort_order')->get();
      $this->boiler = $this->getBoiler();
      $this->addons = Addon::orderBy('sort_order')->get();
      $this->showPrices = $this->page['showPrices'] = KetelSettings::get('showPrices');
      $this->extendedPlugin = $this->page['extendedPlugin'] = KetelSettings::get('extendedPlugin');
    }

    protected function getBoiler()
    {
      $slug = $this->property('slug');
      $boiler = new Boiler;
      $query = $boiler->query();
      $query->where('slug', $slug);
      $boiler = $query->first();

      return $boiler;
    }

    public function onSubmitOffer()
    {
      $data = Input::all();
      $hash_string = input('achternaam') . input('emailadres') . date('d-m-Y-H:m:s');
      $hash = md5($hash_string);
      $data['link'] = $data['offerlink'] . $hash;

      $offer = Offer::create([
        'hash' => $hash,
        'first_name' => $data['voornaam'] ? $data['voornaam'] : '',
        'last_name' => $data['achternaam'] ? $data['achternaam'] : '',
        'street' => $data['straatnaam'] ? $data['straatnaam'] : '',
        'housenumber' => $data['huisnummer'] ? $data['huisnummer'] : '',
        'addition' => $data['toevoeging'] ? $data['toevoeging'] : '',
        'zipcode' => $data['postcode'] ? $data['postcode'] : '',
        'city' => $data['woonplaats'] ? $data['woonplaats'] : '',
        'telephone' => $data['telefoonnummer'] ? $data['telefoonnummer'] : '',
        'email' => $data['emailadres'] ? $data['emailadres'] : '',
        'price' => $data['totalprice'] ? $data['totalprice'] : '',
        'status' => 'Verstuurd',
      ]);

      $offer->boiler()->sync([$data['boiler']]);
      if ( $data['thermostaat'] != 0 || $data['thermostaat'] != '0') {
        $offer->thermostat()->sync([$data['thermostaat']]);
      }
      $offer->addons()->sync($data['addons']);

      $company_mail = KetelSettings::get('company_mail');
      $company_name = KetelSettings::get('company_name');
      $company_replyto = KetelSettings::get('company_replyto');

      if ($company_mail) {

        Mail::send('instalweb.ketels::mail.offer-consumer', $data, function($message) use ($data, $company_mail, $company_name, $company_replyto) {
          $message->from($company_mail, $company_name);
          $message->replyTo($company_replyto ? $company_replyto : $company_mail, $company_name);
          $message->to($data['emailadres']);
        });

        Mail::send('instalweb.ketels::mail.offer-company', $data, function($message) use ($data, $company_mail, $company_name) {
          $message->from($company_mail, $company_name);
          $message->replyTo($data['emailadres']);
          $message->to($company_mail);
        });
      }

    }

}
