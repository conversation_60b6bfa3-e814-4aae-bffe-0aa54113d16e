{% set brands = __SELF__.brands %}
{% set boilers = __SELF__.boilers %}
{% set thermostats = __SELF__.thermostats %}

<div class="py-8 md:py-12 lg:py-16 bg-gradient-to-r from-primary-600 via-primary-500 to-primary-600">
    <div class="container">
        <div class="flex flex-col">
            <h1 class="mt-2 text-4xl font-bold tracking-tight text-white sm:text-6xl order-2 font-display">{{ 'Downloads'|_ }}</h1>
            <span class="text-base font-semibold leading-7 text-primary-50 order-1">{{ 'Product brochures en handleidingen'|_ }}</span>
        </div>
    </div>
</div>

<nav class="flex border-b border-gray-200 bg-white" aria-label="Breadcrumb">
  <ol role="list" class="container flex space-x-4 px-4 sm:px-6 lg:px-8">
    <li class="flex">
      <div class="flex items-center">
        <a href="/" class="text-gray-400 hover:text-gray-500">
          <svg class="h-5 w-5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M9.293 2.293a1 1 0 011.414 0l7 7A1 1 0 0117 11h-1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-3a1 1 0 00-1-1H9a1 1 0 00-1 1v3a1 1 0 01-1 1H5a1 1 0 01-1-1v-6H3a1 1 0 01-.707-1.707l7-7z" clip-rule="evenodd"></path>
          </svg>
          <span class="sr-only">{{ 'Home'|_ }}</span>
        </a>
      </div>
    </li>

    <li class="flex">
      <div class="flex items-center">
        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
          <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z"></path>
        </svg>
        <a href="/cv-ketels" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-600">{{ 'CV Ketels'|_ }}</a>
      </div>
    </li>


    <li class="flex">
      <div class="flex items-center">
        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
          <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z"></path>
        </svg>
        <span class="ml-4 text-sm font-medium text-gray-700" aria-current="page">{{ 'Downloads'|_ }}</span>
      </div>
    </li>
  </ol>
</nav>

<div class="bg-gray-100 py-8 md:py-12 lg:py-16">
    <div class="container">
        <div class="md:grid md:grid-cols-2 md:gap-12">
            <div class="mb-12 md:mb-0">
                <h2 class="mb-9">{{ 'CV ketel documentatie'|_ }}</h2>
                {% for brand in brands %}
                    <div class="mb-12">
                        <h3 class="text-3xl font-semibold text-gray-800 mb-4">{{ brand.title }}</h3>

                        {% for boiler in boilers %}
                            {% if boiler.merken.id == brand.id %}
                                <div class="bg-white p-6 rounded-md border flex mb-4">
                                    <div class="mr-auto">
                                        <h4 class="text-base font-bold text-gray-800 m-0">{{ boiler.title }}</h4>
                                    </div>
                                    <div>
                                        <a href="{{ boiler.brochure | media }}" target="_blank" class="block mb-2  group">
                                            <i class="fa-regular fa-arrow-right text-primary mr-2"></i>
                                            <span class="text-primary-500 group-hover:text-primary-700">{{ 'Download brochure'|_ }}</span>
                                        </a>
                                        <a href="{{ boiler.energy_label_large | media }}" target="_blank" class="block  group">
                                            <i class="fa-regular fa-arrow-right text-primary mr-2"></i>
                                            <span class="text-primary-500 group-hover:text-primary-700">{{ 'Download energielabel'|_ }}</span>
                                        </a>
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                {% endfor %}
            </div>
            <div>
                <h2 class="mb-9">{{ 'Thermostaten documentatie'|_ }}</h2>

                {% for brand in brands %}
                    {% set hasThermostat = false %}
                    {% for thermostat in thermostats %}
                        {% if thermostat.merken.id == brand.id %}
                            {% set hasThermostat = true %}
                        {% endif %}
                    {% endfor %}

                    {% if hasThermostat %}
                    <div class="mb-12">
                        <h3 class="text-3xl font-semibold text-gray-800 mb-4">{{ brand.title }}</h3>

                        {% for thermostat in thermostats %}
                            {% if thermostat.merken.id == brand.id %}
                                <div class="bg-white p-6 rounded-md border flex mb-4">
                                    <div class="mr-auto">
                                        <h4 class="text-base font-bold text-gray-800 m-0">{{ thermostat.title }}</h4>
                                    </div>
                                    <div>
                                        <a href="{{ thermostat.brochure | media }}" target="_blank" class="block mb-2  group">
                                            <i class="fa-regular fa-arrow-right text-primary mr-2"></i>
                                            <span class="text-primary-500 group-hover:text-primary-700">{{ 'Download brochure'|_ }}</span>
                                        </a>
                                        <a href="{{ thermostat.manual | media }}" target="_blank" class="block  group">
                                            <i class="fa-regular fa-arrow-right text-primary mr-2"></i>
                                            <span class="text-primary-500 group-hover:text-primary-700">{{ 'Download handleiding'|_ }}</span>
                                        </a>
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>
