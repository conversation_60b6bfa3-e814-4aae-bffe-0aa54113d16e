<?php namespace Instalweb\Ketels\Components;

use Cms\Classes\ComponentBase;
use Db;
use Instalweb\Ketels\Models\Thermostat;
use Instalweb\Ketels\Models\Brand;

class ThermostatList extends ComponentBase
{
    /**
     * A collection of records to display
     * @var \October\Rain\Database\Collection
     */
    public $brands;
    public $thermostats;



    public function componentDetails()
    {
      return [
        'name'        => 'Thermostaten overzicht',
        'description' => 'Thermostaten overzichtpagina'
      ];
    }

    //
    // Properties
    //

    public function defineProperties()
    {
      return [];
    }
    //
    // Rendering and processing
    //

    public function onRun()
    {
      $this->brands = Brand::where('inactive', false)->where('type', 'boiler')->orderBy('sort_order')->get();
      $this->thermostats = Thermostat::orderBy('sort_order')->get();
    }

}
