<!-- Filters -->
<form
    id="filterform-desktop"
    class="hidden lg:block"
>
    <h3 class="sr-only">{{ 'Categorieën'|_ }}</h3>
    <ul role="list" class="text-sm font-medium text-gray-900 space-y-4 pb-6 pl-3 border-b border-gray-200">
        <li>
            <a href="/cv-ketels">
                {{ 'Alle ketels'|_ }}
            </a>
        </li>
        {% for item in brands %}
            <li>
                <a href="/cv-ketels/{{ item.slug }}" class="{{ brand.id == item.id ? 'text-primary-600' }}">
                    {{ item.title }}
                    {% if item.special %}<span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">{{ 'Actie'|_ }}</span>{% endif %}
                </a>
            </li>
        {% endfor %}
    </ul>

    <!-- Filter -->
    <div x-data="{ open: false }" class="border-b border-gray-200 py-6">
        <h3 class="-my-3 flow-root">
            <button
                type="button"
                class="outline-none py-3 px-3 bg-white w-full flex items-center justify-between text-sm text-gray-400 hover:text-gray-500"
                aria-controls="filter-section-0"
                @click="open = !open" aria-expanded="false"
            >
                <span class="font-medium text-gray-900">
                    {{ 'In welk type woning woont u?'|_ }}
                </span>
                <span class="ml-6 flex items-center">
                    <svg class="h-5 w-5" x-show="!(open)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    <svg class="h-5 w-5" x-show="open" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" style="display: none;">
                        <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </span>
            </button>
        </h3>
        <div class="pt-6" id="filter-section-0" x-show="open" style="display: none;">
            <div class="space-y-4">
                <div class="flex items-center">
                    {% partial '@radio' id="woningtype-0" name="woningtype" value="appartement" noRequest="true" %}
                    <label for="woningtype-0" class="ml-3 text-sm text-gray-600">
                        {{ 'Appartement'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="woningtype-1" name="woningtype" value="tussenwoning" noRequest="true" %}
                    <label for="woningtype-1" class="ml-3 text-sm text-gray-600">
                        {{ 'Tussenwoning'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="woningtype-2" name="woningtype" value="hoekwoning" noRequest="true" %}
                    <label for="woningtype-2" class="ml-3 text-sm text-gray-600">
                        {{ 'Hoekwoning'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="woningtype-3" name="woningtype" value="tweeOnderEenKap" noRequest="true" %}
                    <label for="woningtype-3" class="ml-3 text-sm text-gray-600">
                        {{ '2 onder 1 kap'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="woningtype-4" name="woningtype" value="vrijstaand" noRequest="true" %}
                    <label for="woningtype-4" class="ml-3 text-sm text-gray-600">
                        {{ 'Vrijstaande woning'|_ }}
                    </label>
                </div>

            </div>
        </div>
    </div>

    <!-- Filter -->
    <div x-data="{ open: false }" class="border-b border-gray-200 py-6">
        <h3 class="-my-3 flow-root">
            <button
                type="button"
                class="outline-none py-3 px-3 bg-white w-full flex items-center justify-between text-sm text-gray-400 hover:text-gray-500"
                aria-controls="filter-section-0"
                @click="open = !open" aria-expanded="false"
            >
                <span class="font-medium text-gray-900">
                    {{ 'Wat is uw woningoppervlakte?'|_ }}
                </span>
                <span class="ml-6 flex items-center">
                    <svg class="h-5 w-5" x-show="!(open)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    <svg class="h-5 w-5" x-show="open" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" style="display: none;">
                        <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </span>
            </button>
        </h3>
        <div class="pt-6" id="filter-section-0" x-show="open" style="display: none;">
            <div class="space-y-4">
                <div class="flex items-center">
                    {% partial '@radio' id="oppervlak-01" name="oppervlak" value="oppervlakteExtraKlein" %}
                    <label for="oppervlak-01" class="ml-3 text-sm text-gray-600">
                        {{ '< 80 m2'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="oppervlak-0" name="oppervlak" value="oppervlakteKlein" %}
                    <label for="oppervlak-0" class="ml-3 text-sm text-gray-600">
                        {{ '80 m2 - 120 m2'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="oppervlak-1" name="oppervlak" value="oppervlakteNormaal" %}
                    <label for="oppervlak-1" class="ml-3 text-sm text-gray-600">
                        {{ '120 m2 - 160 m2'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="oppervlak-2" name="oppervlak" value="oppervlakteMedium" %}
                    <label for="oppervlak-2" class="ml-3 text-sm text-gray-600">
                        {{ '160 m2 - 200 m2'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="oppervlak-3" name="oppervlak" value="oppervlakteGroot" %}
                    <label for="oppervlak-3" class="ml-3 text-sm text-gray-600">
                        {{ '200 m2 - 240 m2'|_ }}
                    </label>
                </div>

            </div>
        </div>
    </div>

    <!-- Filter -->
    <div x-data="{ open: false }" class="border-b border-gray-200 py-6">
        <h3 class="-my-3 flow-root">
            <button
                type="button"
                class="outline-none py-3 px-3 bg-white w-full flex items-center justify-between text-sm text-gray-400 hover:text-gray-500"
                aria-controls="filter-section-0"
                @click="open = !open" aria-expanded="false"
            >
                <span class="font-medium text-gray-900">
                    {{ 'Warmwaterbehoefte'|_ }}
                </span>
                <span class="ml-6 flex items-center">
                    <svg class="h-5 w-5" x-show="!(open)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    <svg class="h-5 w-5" x-show="open" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" style="display: none;">
                        <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </span>
            </button>
        </h3>
        <div class="pt-6" id="filter-section-0" x-show="open" style="display: none;">
            <div class="space-y-4">
                <div class="flex items-center">
                    {% partial '@radio' id="waterbehoefte-0" name="waterbehoefte" value="douche" %}
                    <label for="waterbehoefte-0" class="ml-3 text-sm text-gray-600">
                        {{ 'Douche'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="waterbehoefte-1" name="waterbehoefte" value="stortdouche" %}
                    <label for="waterbehoefte-1" class="ml-3 text-sm text-gray-600">
                        {{ 'Stort douche'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="waterbehoefte-2" name="waterbehoefte" value="bad" %}
                    <label for="waterbehoefte-2" class="ml-3 text-sm text-gray-600">
                        {{ 'Bad'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="waterbehoefte-3" name="waterbehoefte" value="grootBad" %}
                    <label for="waterbehoefte-3" class="ml-3 text-sm text-gray-600">
                        {{ 'Groot bad'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="waterbehoefte-4" name="waterbehoefte" value="tweeDouches" %}
                    <label for="waterbehoefte-4" class="ml-3 text-sm text-gray-600">
                        {{ '2 douches'|_ }}
                    </label>
                </div>

                <div class="flex items-center">
                    {% partial '@radio' id="waterbehoefte-5" name="waterbehoefte" value="badDoucheTegelijk" %}
                    <label for="waterbehoefte-5" class="ml-3 text-sm text-gray-600">
                        {{ 'Bad & douche tegelijk'|_ }}
                    </label>
                </div>

            </div>
        </div>
    </div>

</form>
