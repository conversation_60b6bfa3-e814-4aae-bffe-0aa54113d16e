<div class="group relative bg-white border border-gray-200 rounded-lg flex flex-col overflow-hidden">
  <div class="bg-gray-50 group-hover:opacity-75 p-10 sm:h-72">
    <img src="{{ item.image|media|resize(200) }}" alt="" class="w-full h-full object-center object-cover sm:w-auto sm:h-full block mx-auto">
  </div>
  <div class="flex-1 p-4 space-y-2 flex flex-col">
    <h3 class="text-base font-medium text-gray-900">
      <a href="/cv-ketels/{{ item.merken.slug}}/{{ item.slug }}">
        <span aria-hidden="true" class="absolute inset-0"></span>
        <span class="font-bold">{{ item.merken.title }}</span> {{ item.title }}
      </a>
    </h3>

    <div class="flex-1 flex flex-col justify-end">
      {% if item.price > 0 %}
        <p class="text-base text-gray-600">{{ 'v.a.'|_ }} <span class="font-medium text-primary">{{ item.price }}</span></p>
      {% else %}
        <p class="text-base font-medium text-primary">{{ 'Prijs op aanvraag'|_ }}</p>
      {% endif %}

    </div>

    <p class="text-sm text-gray-500">
      {% if item.comfort_level == "3" %}
      {{ 'Voor kleine woningen'|_ }}
      {% elseif item.comfort_level == "4" %}
      {{ 'Voor middelgrote woningen'|_ }}
      {% else %}
      {{ 'Voor grote woningen'|_ }}
      {% endif %} <br>
      {{ 'Vermogen:'|_ }} {{ item.capacity }} {{ 'kw'|_ }}
    </p>

    <div class="pt-4">
      <button type="button" class="inline-flex items-center px-8 py-1.5 border border-transparent text-sm font-medium rounded shadow-sm text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
        {{ 'Bekijk ketel'|_ }}
      </button>
    </div>
  </div>
</div>
