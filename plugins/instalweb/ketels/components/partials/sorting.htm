<div class="relative inline-block text-left" x-data="{ open: false }">
  <div>
    <button type="button" class="group inline-flex justify-center text-sm font-medium text-gray-700 hover:text-gray-900 focus:ring-0 outline-none" id="menu-button" aria-expanded="false" aria-haspopup="true" @click="open = ! open" :class="{ 'text-gray-900': open, 'text-gray-700': !(open) }">
        {{ 'Sorteer'|_ }}
        <!-- Heroicon name: solid/chevron-down -->
        <svg class="flex-shrink-0 -mr-1 ml-1 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
    </button>
  </div>

  <div
    class="origin-top-right absolute right-0 mt-2 w-44 rounded-md shadow-2xl bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
    role="menu"
    aria-orientation="vertical"
    aria-labelledby="menu-button"
    tabindex="-1"
    x-show="open"
    x-transition:enter="transition ease-out duration-100"
    x-transition:enter-start="transform opacity-0 scale-95"
    x-transition:enter-end="transform opacity-100 scale-100"
    x-transition:leave="transition ease-in duration-75"
    x-transition:leave-start="transform opacity-100 scale-100"
    x-transition:leave-end="transform opacity-0 scale-95"
    x-on:click.away="open = false"
    x-cloak
  >
    <div class="py-1" role="none">
      <!--
        Active: "bg-gray-100", Not Active: ""
        Selected: "font-medium text-gray-900", Not Selected: "text-gray-500"
      -->
      <a href="#" class="hover:bg-gray-100 font-medium text-gray-900 block px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-0">
          {{ 'Meest gekozen'|_ }}
      </a>

      <a href="#" class="hover:bg-gray-100 text-gray-500 block px-4 py-2 text-sm hover:text-gray-600" role="menuitem" tabindex="-1" id="menu-item-1">
          {{ 'Vermogen'|_ }}
      </a>

      <a href="#" class="hover:bg-gray-100 text-gray-500 block px-4 py-2 text-sm hover:text-gray-600" role="menuitem" tabindex="-1" id="menu-item-2">
          {{ 'Nieuwste'|_ }}
      </a>

      <a href="#" class="hover:bg-gray-100 text-gray-500 block px-4 py-2 text-sm hover:text-gray-600" role="menuitem" tabindex="-1" id="menu-item-3">
          {{ 'Prijs: Laag naar Hoog'|_ }}
      </a>

      <a href="#" class="hover:bg-gray-100 text-gray-500 block px-4 py-2 text-sm hover:text-gray-600" role="menuitem" tabindex="-1" id="menu-item-4">
          {{ 'Prijs: Hoog naar Laag'|_ }}
      </a>
    </div>
  </div>
</div>