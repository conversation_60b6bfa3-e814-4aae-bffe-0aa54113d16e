<?php namespace Instalweb\Ketels\Components;

use Cms\Classes\ComponentBase;
use Db;
use Instalweb\Ketels\Models\Boiler;
use Instalweb\Ketels\Models\Brand;
use Instalweb\Ketels\Models\Thermostat;

class DownloadsList extends ComponentBase
{
    /**
     * A collection of records to display
     * @var \October\Rain\Database\Collection
     */
    public $brands;
    public $boilers;
    public $thermostats;



    public function componentDetails()
    {
      return [
        'name'        => 'Downloads overzicht',
        'description' => 'Documentatie van ketels en thermostaten'
      ];
    }

    //
    // Properties
    //

    public function defineProperties()
    {
      return [];
    }
    //
    // Rendering and processing
    //

    public function onRun()
    {
      $this->brands = Brand::where('inactive', false)->orderBy('sort_order')->get();
      $this->boilers = Boiler::orderBy('sort_order')->get();
      $this->thermostats = Thermostat::orderBy('sort_order')->get();
    }

}
