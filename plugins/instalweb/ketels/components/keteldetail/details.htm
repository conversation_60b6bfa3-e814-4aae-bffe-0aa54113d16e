{% macro euro(number) %}
    &euro; {{ number | number_format(2, ',', '.') }}
{% endmacro %}
{% import _self as format %}

<div class="mx-auto mt-14 max-w-2xl sm:mt-16 lg:col-span-3 lg:row-span-2 lg:row-end-2 lg:mt-0 lg:max-w-none">
        <div class="flex flex-col-reverse">
          <div class="mt-4">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">{{ ketel.merken.title }} {{ ketel.title }}</h1>
            {% if ketel.price > 0 %}
            <p class="text-primary mt-2">{{ 'Vanaf'|_ }} {{ format.euro(ketel.price) }}</p>
            {% else %}
            <p class="text-primary mt-2">{{ 'Prijs op aanvraag'|_ }}</p>
            {% endif %}
            <h2 id="information-heading" class="sr-only">{{ 'Ketel informatie'|_ }}</h2>
          </div>

          {% if ketel.merken.logo %}
          <div>
            <h3 class="sr-only">Merk</h3>
            <a href="/cv-ketels/{{ ketel.merken.slug }}" title="Meer ketels van {{ ketel.merken.title }}"><img src="{{ ketel.merken.logo|media }}" alt="{{ ketel.merken.title }}" class="h-10"></a>
          </div>
          {% endif %}
        </div>

        <p class="mt-6 text-gray-500">{{ ketel.short_description }}</p>

        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2" x-data="openClose">
          {% if ketel.price > 0 and extendedPlugin %}
            <a href="#ketelofferte" class="flex w-full items-center justify-center rounded-md border border-transparent bg-primary-500 py-3 px-8 text-base font-medium text-white hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50" >{{ 'Offerte aanvragen'|_ }}</a>
          {% else %}
            <button type="button" class="flex w-full items-center justify-center rounded-md border border-transparent bg-primary-500 py-3 px-8 text-base font-medium text-white hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50" @click="toggle">{{ 'Prijs aanvragen'|_ }}</button>
          {% endif %}

          <a href="/contact" title="{{ 'Vraag om advies'|_ }}" class="flex w-full items-center justify-center rounded-md border border-transparent bg-primary-100 py-3 px-8 text-base font-medium text-primary-700 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-50">{{ 'Vraag om advies'|_ }}</a>

          {% if ketel.price > 0 %}
            {% partial '@form_modal' ketel=ketel type="offer" %}
          {% else %}
            {% partial '@form_modal' ketel=ketel type="price" %}
          {% endif %}
        </div>

        {% if ketel.comfort_level or ketel.capacity or ketel.tap_capacity_40 %}
        <div class="mt-10 border-t border-gray-200 pt-10">
            <h3 class="text-sm font-medium text-gray-900">In het kort</h3>
            <div class="prose prose-sm mt-4 text-gray-500">
                <ul role="list">
                    {% if ketel.comfort_level %}
                        <li><span class="font-medium">{{ 'Comfort waarde:'|_ }}</span> {{ ketel.comfort_level }}</li>
                    {% endif %}
                    {% if ketel.capacity %}
                        <li><span class="font-medium">{{ 'Capaciteit:'|_ }}</span> {{ ketel.capacity }}{{ 'kw'|_ }}</li>
                    {% endif %}
                    {% if ketel.tap_capacity_40 %}
                        <li><span class="font-medium">{{ 'Tapcapaciteit bij 40°C:'|_ }}</span> {{ ketel.tap_capacity_40 }}
                    {{ 'liter/min'|_ }}</li>
                    {% endif %}
                </ul>
            </div>
        </div>
        {% endif %}

        {% if ketel.energy_label_heating or ketel.energy_label_water %}
        <div class="mt-10 border-t border-gray-200 pt-10">
          <h3 class="text-sm font-medium text-gray-900">{{ 'Energielabel'|_ }}</h3>
          <div class="flex space-x-2 mt-4 items-center">
            {% if ketel.energy_label_heating %}<div><img src="{{ ketel.energy_label_heating | media }}" alt="Energielabel voor ruimteverwarming" class="h-10"></div>{% endif %}
            {% if ketel.energy_label_water %}<div><img src="{{ ketel.energy_label_water | media }}" alt="Energielabel voor water verwarming" class="h-10"></div>{% endif %}
          </div>
          {% if ketel.energy_label_large %}<p class="mt-4 text-sm text-gray-500"><a href="{{ ketel.energy_label_large | media }}" target="_blank" class="font-medium text-primary-600 hover:text-primary-500">
              {{ 'Bekijk energielabel'|_ }}</a></p>{% endif %}
        </div>
        {% endif %}


      </div>
