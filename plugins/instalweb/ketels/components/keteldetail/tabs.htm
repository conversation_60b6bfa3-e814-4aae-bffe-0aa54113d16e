<div class="mx-auto mt-16 w-full max-w-2xl lg:col-span-4 lg:mt-0 lg:max-w-none">
  <div x-data="tabs" x-id="['tab']">
    <div class="border-b border-gray-200">
      <ul
        class="-mb-px flex space-x-8"
        aria-orientation="horizontal"
        role="tablist"
        x-ref="tablist"
        @keydown.right.prevent.stop="$focus.wrap().next()"
        @keydown.home.prevent.stop="$focus.first()"
        @keydown.page-up.prevent.stop="$focus.first()"
        @keydown.left.prevent.stop="$focus.wrap().prev()"
        @keydown.end.prevent.stop="$focus.last()"
        @keydown.page-down.prevent.stop="$focus.last()"
      >

        <li>
          <button
            :id="$id('tab', whichChild($el.parentElement, $refs.tablist))"
            @click="select($el.id)"
            @mousedown.prevent
            @focus="select($el.id)"
            :tabindex="isSelected($el.id) ? 0 : -1"
            :aria-selected="isSelected($el.id)"
            :class="isSelected($el.id) ? 'border-primary-600 text-primary-600' : 'border-transparent text-gray-700 hover:text-gray-800 hover:border-gray-300'"
            class="whitespace-nowrap border-b-2 py-6 text-sm font-medium"
            role="tab"
            type="button"
          >
            {{ 'Ketel omschrijving'|_ }}
          </button>
        </li>

        <li>
          <button
            :id="$id('tab', whichChild($el.parentElement, $refs.tablist))"
            @click="select($el.id)"
            @mousedown.prevent
            @focus="select($el.id)"
            :tabindex="isSelected($el.id) ? 0 : -1"
            :aria-selected="isSelected($el.id)"
            :class="isSelected($el.id) ? 'border-primary-600 text-primary-600' : 'border-transparent text-gray-700 hover:text-gray-800 hover:border-gray-300'"
            class="whitespace-nowrap border-b-2 py-6 text-sm font-medium"
            role="tab"
            type="button"
          >
            {{ 'Geschikte thermostaten'|_ }}
          </button>
        </li>

        <li>
          <button
            :id="$id('tab', whichChild($el.parentElement, $refs.tablist))"
            @click="select($el.id)"
            @mousedown.prevent
            @focus="select($el.id)"
            :tabindex="isSelected($el.id) ? 0 : -1"
            :aria-selected="isSelected($el.id)"
            :class="isSelected($el.id) ? 'border-primary-600 text-primary-600' : 'border-transparent text-gray-700 hover:text-gray-800 hover:border-gray-300'"
            class="whitespace-nowrap border-b-2 py-6 text-sm font-medium"
            role="tab"
            type="button"
          >
            {{ 'Specificaties'|_ }}
          </button>
        </li>

        {% if ketel.brochure or ketel.energy_label_large %}
        <li>
          <button
            :id="$id('tab', whichChild($el.parentElement, $refs.tablist))"
            @click="select($el.id)"
            @mousedown.prevent
            @focus="select($el.id)"
            :tabindex="isSelected($el.id) ? 0 : -1"
            :aria-selected="isSelected($el.id)"
            :class="isSelected($el.id) ? 'border-primary-600 text-primary-600' : 'border-transparent text-gray-700 hover:text-gray-800 hover:border-gray-300'"
            class="whitespace-nowrap border-b-2 py-6 text-sm font-medium"
            role="tab"
            type="button"
          >
            {{ 'Documentatie'|_ }}
          </button>
        </li>
        {% endif %}
      </ul>
    </div>

    <div role="tabpanels pt-8">

      <div x-show="isSelected($id('tab', whichChild($el, $el.parentElement)))" :aria-labelledby="$id('tab', whichChild($el, $el.parentElement))"role="tabpanel" tabindex="0">
        <div class="mt-3">
            <h3 class="text-xl font-bold mb-5">{{ 'Ketel omschrijving'|_ }}</h3>
        </div>
        <div class="prose max-w-none">
          {{ ketel.description|raw }}
        </div>
      </div>

      <div x-show="isSelected($id('tab', whichChild($el, $el.parentElement)))" :aria-labelledby="$id('tab', whichChild($el, $el.parentElement))"role="tabpanel" tabindex="0">
        <div class="mt-3">
            <h3 class="text-xl font-bold mt-3 mb-5">{{ 'Geschikte thermostaten'|_ }}</h3>
        </div>
        {% for item in ketel.thermostats %}
          {% partial '@thermostat' item=item %}
        {% endfor %}
      </div>

      <div x-show="isSelected($id('tab', whichChild($el, $el.parentElement)))" :aria-labelledby="$id('tab', whichChild($el, $el.parentElement))"role="tabpanel" tabindex="0">
        <div class="mt-3">
            <h3 class="text-xl font-bold mt-3 mb-5">{{ 'Specificaties'|_ }}</h3>
        </div>
        {% partial '@specs' ketel=ketel %}
      </div>

      {% if ketel.brochure or ketel.energy_label_large %}
      <div x-show="isSelected($id('tab', whichChild($el, $el.parentElement)))" :aria-labelledby="$id('tab', whichChild($el, $el.parentElement))"role="tabpanel" tabindex="0">
        <div class="mt-3">
            <h3 class="text-xl font-bold mt-3 mb-5">{{ 'Documentatie'|_ }}</h3>
        </div>

        {% if ketel.brochure %}<a href="{{ ketel.brochure | media }}" target="_blank" class="block mb-2 hover:text-secondary-600 group"><i class="fa-regular fa-arrow-right text-primary mr-2"></i> <span class="group-hover:text-primary-400">{{ 'Download brochure'|_ }}</span></a>{% endif %}
        {% if ketel.energy_label_large %}<a href="{{ ketel.energy_label_large | media }}" target="_blank" class="block mb-2 hover:text-secondary-600 group"><i class="fa-regular fa-arrow-right text-primary mr-2"></i> <span class="group-hover:text-primary-400">{{ 'Download energielabel'|_ }}</span></a>{% endif %}
      </div>
      {% endif %}

    </div>
  </div>
</div>
