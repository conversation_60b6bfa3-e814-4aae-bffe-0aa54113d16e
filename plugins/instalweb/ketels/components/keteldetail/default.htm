{% set ketel = __SELF__.boiler %}
{% macro euro(number) %}
	&euro; {{ number | number_format(2, ',', '.') }}
{% endmacro %}
{% import _self as format %}

<div class="bg-white">
	<div class="mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:max-w-7xl lg:px-8">
		<!-- Product -->
		<div class="lg:grid lg:grid-cols-7 lg:grid-rows-1 lg:gap-x-8 lg:gap-y-10 xl:gap-x-16">
			<!-- Product image -->
			<div class="lg:col-span-4 lg:row-end-1">
				<div class="aspect-w-4 aspect-h-3 overflow-hidden rounded-lg bg-gray-100">
					<div class="p-10 flex items-center justify-center">
						<img src="{{ ketel.image|media|resize(600) }}" alt="{{ ketel.merken.title }} {{ ketel.title }}" class="w-auto h-full">
					</div>
				</div>
			</div>

			<!-- Product details -->
			{% partial '@details' ketel=ketel %}

			<!-- Tabs -->
			{% partial '@tabs' ketel=ketel %}

		</div>
	</div>
</div>

{% if extendedPlugin %}
	{% partial '@offer' ketel=ketel %}
{% endif %}
