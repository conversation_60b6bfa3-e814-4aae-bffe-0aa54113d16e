<?php namespace Instalweb\Ketels\Models;

use Cms\Classes\Page;
use Model;

/**
 * KetelSettings Model
 *
 * @link https://docs.octobercms.com/3.x/extend/settings/model-settings.html
 */
class KetelSettings extends \System\Models\SettingModel
{
    public $settingsCode = 'instalweb_ketel_settings';

    public $settingsFields = 'fields.yaml';

    public function initSettingsData()
    {
        $this->showPrices = 0;
        $this->extendedPlugin = 0;
        $this->ketelPage = "october:\/\/cms-page@link\/ketels\/cv-ketel?title=CV+Ketel+%28ketels%2Fcv-ketel%29";
        $this->terms_of_service = "<p><strong>De volgende werkzaamheden zijn inbegrepen bij een all-in installatieprijs:<\/strong><\/p><ul><li>Het aftappen van uw verwarmingsinstallatie zover als nodig voor de vervanging<\/li><li>Het demonteren en afvoeren van uw oude HR ketel<\/li><li>Het demonteren en afvoeren van uw oude expansievat<\/li><li>Het monteren van de gekozen nieuwe HR ketel<\/li><li>Het monteren van een expansievat 18ltr op bestaande expansievatbeugel<\/li><li>Het monteren van aansluitkranen (gaskogelkraan, inlaatcombinatie, veiligheidsventiel en vul\/aftapkraan op de cv leiding<\/li><li>Het monteren en aansluiten van verwarmingsleidingen (2x dunwandig cv buis), waterleidingen 15mm (2x), gasleiding en pvc afvoer (min. 40mm) met benodigde koppelingen<\/li><li>Het aansluiten van uw bestaande kamerthermostaat(draad) op de nieuwe ketel<\/li><li>Het aansluiten van de nieuwe ketel op de bestaande HR rookgasdoorvoer en broekstuk (2x 80mm)<\/li><li>Het aansluiten van de nieuwe ketel op bestaande 230V (geaarde) wandcontactdoos<\/li><li>Alle aan te sluiten onderdelen bevinden zich in de zelfde ruimte als waar het toestel wordt geplaatst<\/li><li>Alle aan te sluiten leidingen bevinden zich binnen één meter van de ketel<\/li><li>Het vullen en in bedrijf stellen van de nieuwe ketel<\/li><li>Onze monteur komt alleen. Indien nodig dient u zelf iemand beschikbaar te hebben om te helpen de oude ketel te verwijderen en de nieuwe op te hangen<\/li><li>Eventuele parkeerkosten zijn voor rekening van de klant<\/li><li>Prijs is voor vervanging binnen 20 km vanaf onze vestiging<\/li><li>De opstellingsplaats van de cv ketel dient aan de voorschriften te voldoen<\/li><li>Montage van de ketel door eigen vakbekwame monteurs<\/li><\/ul>";
        $this->introduction = "<p>Hierbij sturen wij u de vrijblijvende offerte betreffende het vervangen van uw cv ketel.<\/p><p>Deze offerte is geldig tot 1 maand na offertedatum.<\/p><p>Om onduidelijkheden te voorkomen hebben wij voor u alle posten zo uitgebreid mogelijk proberen weer te geven.<\/p><p>Mocht u nog wijzigingen willen in deze offerte, dan horen wij dat graag, zodat we de offerte naar uw wens kunnen aanpassen.<\/p><p>De werkzaamheden zullen het volgende omvatten:<\/p><ul><li>Het demonteren en afvoeren van de oude cv ketel.<\/li><li>Het leveren, monteren en aansluiten van de producten die hieronder staan aangegeven.<\/li><\/ul><p>Hartelijk dank voor deze offerte aanvraag, wij zien uw reactie met belangstelling tegemoet.<br><br><\/p><p>Met vriendelijke groet,<br>||bedrijfsnaam||<\/p>";
        $this->acceptance = "<p>Voordat u dit formulier ondertekent, vragen wij u de offerte goed door te lezen en bij onduidelijkheden contact met ons op te nemen. Als u akkoord wilt geven op deze offerte, kunt hieronder het veld aanvinken en versturen.<\/p><p>Na ontvangst van de opdrachtbevestiging worden de goederen in bestelling gezet en neemt onze afdeling planning contact met u op om een afspraak te plannen. Speciaal voor u bestelde goederen kunnen wij helaas niet retour nemen.<\/p><p>Eventueel genoemde levertijden zijn altijd ter indicatie.<\/p><p>Bij ondertekening van deze offerte, staat de prijs van <strong>&nbsp;||prijs|| (incl. BTW)<\/strong> vast, ongeacht het daadwerkelijk ge-\/verbruik van goederen en diensten. Alle eventuele extra werkzaamheden en materialen die redelijkerwijs buiten deze offerte vallen, is meerwerk. Na overleg zal dit meerwerk aan u worden doorberekend.<\/p>";
    }


    public function getPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }

}
