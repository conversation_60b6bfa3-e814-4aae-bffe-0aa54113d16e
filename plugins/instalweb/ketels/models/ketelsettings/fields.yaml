tabs:
    fields:
        _pages:
            label: 'Ketel pagina'
            type: section
            tab: <PERSON><PERSON><PERSON>'s
        ketelPage:
            label: 'Ketel pagina'
            type: pagefinder
            tab: Pagina's
        _pages_end:
            type: ruler
            tab: Pagina's
        _general:
            label: 'Algemeen'
            type: section
            tab: Algemeen
        showPrices:
            label: 'P<PERSON><PERSON><PERSON> tonen'
            type: switch
            tab: Algemeen
        extendedPlugin:
            label: 'Uitgebreide plugin'
            type: switch
            tab: Algemeen
        _general_end:
            type: ruler
            tab: Algemeen
        _texts:
            label: 'Teksten'
            type: section
            tab: Teksten
        introduction:
            label: 'Introductie'
            type: richeditor
            size: huge
            tab: Teksten
        terms_of_service:
            label: 'Voorwaarden'
            type: richeditor
            size: huge
            tab: Teksten
        acceptance:
            label: 'Opdrachtbevestiging'
            type: richeditor
            size: huge
            tab: Teksten
        _texts_end:
            type: ruler
            tab: Teksten
        _notifications:
            label: 'Notificaties'
            type: section
            tab: Notificaties
        company_name:
            label: '<PERSON><PERSON><PERSON><PERSON><PERSON>am'
            tab: Notificaties
        company_mail:
            label: 'E-mailad<PERSON>'
            tab: Notificaties
        company_replyto:
            label: 'Reply-to e-mailadres'
            tab: Notificaties
        _notifications_end:
            type: ruler
            tab: Notificaties
        _sync:
            label: 'Sync'
            type: section
            tab: Sync
        isApiEndpoint:
            label: 'Is API endpoint'
            type: switch
            tab: Sync
            default: false
        syncWithDomain:
            label: 'Sync with domain'
            tab: Sync
            trigger:
                action: show
                field: isApiEndpoint
                condition: unchecked
        authToken:
            label: 'Auth Token (required, min 12 max 48)'
            min: 12
            max: 48
            tab: Sync
        _sync_end:
            type: ruler
            tab: Sync
