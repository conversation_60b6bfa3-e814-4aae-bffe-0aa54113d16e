<?php namespace Instalweb\Ketels\Models;

use \Instalweb\Ketels\Models\Boiler;

class BoilerImport extends \Backend\Models\ImportModel
{
    public $rules = [];

    public function importData($results, $sessionKey = null)
    {
        foreach ($results as $row => $data) {
            try {
                $boiler = new Boiler;
                $boiler->fill($data);
                $boiler->save();

                $this->logCreated();
            }
            catch (\Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
