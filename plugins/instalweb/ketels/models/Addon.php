<?php namespace Instalweb\Ketels\Models;

use Model;

class Addon extends Model
{
    use \October\Rain\Database\Traits\Validation;
    use \October\Rain\Database\Traits\SoftDelete;
    use \October\Rain\Database\Traits\Sortable;
    use \October\Rain\Database\Traits\SimpleTree;

    protected $dates = ['deleted_at'];

    public $table = 'instalweb_ketels_addons';

    public $rules = [
    ];

    public $belongsToMany = [
        'offers' => [
            \Instalweb\Ketels\Models\Offer::class,
            'table' => 'instalweb_ketels_addon_offer',
        ]
    ];

}
