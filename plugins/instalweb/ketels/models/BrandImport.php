<?php namespace Instalweb\Ketels\Models;

use \Instalweb\Ketels\Models\Brand;

class BrandImport extends \Backend\Models\ImportModel
{
    public $rules = [];

    public function importData($results, $sessionKey = null)
    {
        foreach ($results as $row => $data) {
            try {
                $brand = new Brand;
                $brand->fill($data);
                $brand->save();

                $this->logCreated();
            }
            catch (\Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
