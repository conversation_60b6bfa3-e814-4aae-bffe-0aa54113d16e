<?php namespace Instalweb\Ketels\Models;

use Model;

class Offer extends Model
{
    use \October\Rain\Database\Traits\Validation;
    use \October\Rain\Database\Traits\SoftDelete;

    protected $dates = ['deleted_at'];

    public $table = 'instalweb_ketels_offers';

    public $rules = [
    ];

    protected $fillable = ['hash','first_name', 'last_name', 'street', 'housenumber', 'addition', 'zipcode', 'city', 'telephone', 'email', 'price', 'service_contract', 'status', 'approved_date'];
    
    public $belongsToMany = [
        'boiler' => [
            \Instalweb\Ketels\Models\Boiler::class,
            'table' => 'instalweb_ketels_boiler_offer',
        ],
        'thermostat' => [
            \Instalweb\Ketels\Models\Thermostat::class,
            'table' => 'instalweb_ketels_offer_therm',
        ],
        'addons' => [
            \Instalweb\Ketels\Models\Addon::class,
            'table' => 'instalweb_ketels_addon_offer',
        ],
    ];
}
