<?php namespace Instalweb\Ketels\Models;

use \Instalweb\Ketels\Models\Thermostat;

class ThermostatImport extends \Backend\Models\ImportModel
{
    public $rules = [];

    public function importData($results, $sessionKey = null)
    {
        foreach ($results as $row => $data) {
            try {
                $thermostat = new Thermostat;
                $thermostat->fill($data);
                $thermostat->save();

                $this->logCreated();
            }
            catch (\Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
