<?php namespace Instalweb\Ketels\Models;

use \Instalweb\Ketels\Models\Boiler;

class BoilerExport extends \Backend\Models\ExportModel
{
    public $rules = [];

    public function exportData($columns, $sessionKey = null)
    {
        $boilers = Boiler::all();

        $boilers->each(function($boiler) use ($columns) {
            $boiler->addVisible($columns);
        });

        return $boilers->toArray();
    }
}
