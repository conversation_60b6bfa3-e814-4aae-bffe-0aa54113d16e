<?php

namespace Instalweb\Ketels\Models;

use Cms\Classes\Controller;
use Cms\Classes\Page as CmsPage;
use Cms\Classes\Theme;
use Model;
use October\Rain\Database\Traits\Nullable;
use October\Rain\Database\Traits\SimpleTree;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;

/**
 * Model
 */
class Boiler extends Model
{
    use Nullable;
    use SimpleTree;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected $dates = ['deleted_at'];

    protected $jsonable = ['seo'];

    protected $slugs = ['slug' => 'title'];

    protected $fillable = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'sort_order',
        'parent_id',
        'merken_id',
        'title',
        'slug',
        'description',
        'image',
        'price',
        'brochure',
        'comfort_level',
        'type',
        'tap_capacity_60',
        'tap_capacity_40',
        'capacity',
        'weight',
        'size',
        'efficiency',
        'a_label_pump',
        'warranty_heat_exchanger',
        'energy_label_heating',
        'energy_label_large',
        'inactive',
        'thermostats_id',
        'short_description',
        'energy_label_water',
        'series',
        'seo',
    ];

    protected $nullable = [
        'title',
        'slug',
        'inactive',
        'price',
        'image',
        'short_description',
        'description',
        'series',
        'type',
        'comfort_level',
        'tap_capacity_60',
        'tap_capacity_40',
        'capacity',
        'weight',
        'size',
        'efficiency',
        'a_label_pump',
        'warranty_heat_exchanger',
        'brochure',
        'energy_label_heating',
        'energy_label_water',
        'energy_label_large',
        'seo',
    ];

    public $mediaAttributes = [
        'image',
        'brochure',
        'energy_label_heating',
        'energy_label_water',
        'energy_label_large',
    ];

    public static $syncIgnoredAttributes = [
        'price',
        'inactive',
    ];

    /**
     * @var string The database table used by the model.
     */
    public $table = 'instalweb_ketels_ketels';

    /**
     * @var array Validation rules
     */
    public $rules = [
        'title' => ['required'],
        'slug' => ['required'],
    ];

    public $belongsTo = [
        'merken' => Brand::class,
    ];

    public $belongsToMany = [
        'thermostats' => [
            Thermostat::class,
            'table' => 'instalweb_ketels_b_t',
        ],
        'offers' => [
            Offer::class,
            'table' => 'instalweb_ketels_boiler_offer',
        ],
    ];

    public static function getMenuTypeInfo($type)
    {
        $result = [];

        if ($type == 'ketel-boiler') {
            $references = [];

            $posts = self::orderBy('title')->get();
            foreach ($posts as $post) {
                $references[$post->id] = $post->title;
            }

            $result = [
                'references' => $references,
                'nesting' => false,
                'dynamicItems' => false,
            ];
        }

        if ($type == 'all-ketel-boilers') {
            $result = [
                'nesting' => true,
                'dynamicItems' => true,
            ];
        }

        if ($result) {
            $theme = Theme::getActiveTheme();

            $pages = CmsPage::listInTheme($theme, true);
            $cmsPages = [];

            foreach ($pages as $page) {
                if (! $page->hasComponent('ketelDetail')) {
                    continue;
                }

                $properties = $page->getComponentProperties('ketelDetail');
                if (! preg_match('/{{\s*:/', $properties['slug'])) {
                    continue;
                }

                $cmsPages[] = $page;
            }

            $result['cmsPages'] = $cmsPages;
        }

        return $result;
    }

    public static function resolveMenuItem($item, $url, $theme)
    {
        $result = null;

        if ($item->type == 'ketel-boiler') {
            $model = Boiler::find($item->reference);

            if (! $model) {
                return;
            }

            $controller = new Controller($theme);
            $pageUrl = $controller->pageUrl($item->cmsPage, [
                'id' => $model->id,
                'slug' => $model->slug,
            ]);

            $result = [
                'url' => $pageUrl,
                'isActive' => $pageUrl == $url,
                'title' => $model->title,
                'mtime' => $model->updated_at,
            ];

            return $result;
        } elseif ($item->type == 'all-ketel-boilers') {
            $result = [
                'items' => [],
            ];

            $posts = self::orderBy('title')->get();
            $controller = new Controller($theme);

            foreach ($posts as $post) {
                $pageUrl = $controller->pageUrl($item->cmsPage, [
                    'id' => $post->id,
                    'slug' => $post->slug,
                ]);

                $postItem = [
                    'title' => $post->title,
                    'url' => $pageUrl,
                    'mtime' => $post->updated_at,
                ];

                $postItem['isActive'] = $postItem['url'] == $url;

                $result['items'][] = $postItem;
            }
        }

        return $result;
    }
}
