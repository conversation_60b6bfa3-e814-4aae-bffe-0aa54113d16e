<div data-control="toolbar">
    <a href="<?= Backend::url('instalweb/ketels/thermostats/create') ?>"
       class="btn btn-primary oc-icon-plus"><?= e(trans('backend::lang.form.create')) ?></a>
    <a href="<?= Backend::url('instalweb/ketels/thermostats/reorder') ?>"
       class="btn btn-default oc-icon-list"><?= e(trans('backend::lang.reorder.default_title')) ?></a>

    <?php
        $ketelSettings = \Instalweb\Ketels\Models\KetelSettings::instance();
        if (isset($ketelSettings['syncWithDomain'], $ketelSettings['authToken'])): ?>
    <button
        class="btn btn-outline-info oc-icon-refresh"
        data-request="onSync"
        data-request-confirm="<?= e(trans('Are you sure you want to synchronize?')) ?>"
        data-stripe-load-indicator>
        <?= e(trans('Synchronize')) ?>
    </button>
    <?php endif; ?>

    <button
        class="btn btn-default oc-icon-trash-o"
        disabled="disabled"
        onclick="$(this).data('request-data', {
            checked: $('.control-list').listWidget('getChecked')
        })"
        data-request="onDelete"
        data-request-confirm="<?= e(trans('backend::lang.list.delete_selected_confirm')) ?>"
        data-trigger-action="enable"
        data-trigger=".control-list input[type=checkbox]"
        data-trigger-condition="checked"
        data-request-success="$(this).prop('disabled', true)"
        data-stripe-load-indicator>
        <?= e(trans('backend::lang.list.delete_selected')) ?>
    </button>
    <button
        class="btn btn-default oc-icon-clone"
        disabled="disabled"
        onclick="$(this).data('request-data', {
            checked: $('.control-list').listWidget('getChecked')
        })"
        data-request="onDuplicate"
        data-request-confirm="Do you wanna duplicate this event?"
        data-trigger-action="enable"
        data-trigger=".control-list input[type=checkbox]"
        data-trigger-condition="checked"
        data-request-success="$(this).prop('disabled', true)"
        data-stripe-load-indicator>
        Duplicate selected
    </button>

    <a href="<?= Backend::url('instalweb/ketels/thermostats/import') ?>" class="btn btn-default oc-icon-upload">Importeer</a>

    <a href="<?= Backend::url('instalweb/ketels/thermostats/export') ?>" class="btn btn-default oc-icon-download">Exporteer</a>
</div>
