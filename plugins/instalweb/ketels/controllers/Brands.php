<?php

namespace Instalweb\Ketels\Controllers;

use Artisan;
use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Flash;

class Brands extends Controller
{
    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public $listConfig = 'config_list.yaml';

    public $formConfig = 'config_form.yaml';

    public $importExportConfig = 'config_import_export.yaml';

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Instalweb.Ketels', 'main-menu-item', 'side-menu-item');
    }

    public function onSync()
    {
        // Voer de synchronisatie uit
        $result = Artisan::call('instalweb:ketel:sync-from-api-endpoint');

        // Toon een flash bericht
        Flash::success('Merken zijn gesynchroniseerd.');

        // Her<PERSON><PERSON> de lijst
        return $this->listRefresh();
    }
}
