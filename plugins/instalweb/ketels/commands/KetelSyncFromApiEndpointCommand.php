<?php

namespace App\Console\Commands;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Instalweb\Ketels\Models\Boiler;
use Instalweb\Ketels\Models\Brand;
use Instalweb\Ketels\Models\KetelSettings;
use Instalweb\Ketels\Models\Thermostat;
use JsonException;

class KetelSyncFromApiEndpointCommand extends Command
{
    protected $signature = 'instalweb:ketel:sync-from-api-endpoint';

    protected $description = 'Sync all from endpoint';

    protected $syncWithDomain;

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function handle(): void
    {
        $ketelSettings = KetelSettings::instance();

        if (! isset($ketelSettings['syncWithDomain'], $ketelSettings['authToken'])) {
            return;
        }

        $this->syncWithDomain = $ketelSettings['syncWithDomain'];

        $client = new Client([
            'base_uri' => "https://{$ketelSettings->syncWithDomain}/cv-ketels/api/sync/",
            'headers' => [
                'Authorization' => 'Bearer '.$ketelSettings['authToken'],
            ],
        ]);

        $this->syncBoilers($client);
        $this->syncBrands($client);
        $this->syncThermostats($client);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function get(Client $client, string $url)
    {
        $response = $client->request('GET', $url);

        $decodedResponse = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

        if ($decodedResponse['success']) {
            return $decodedResponse['resources'];
        }
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function syncBoilers(Client $client): void
    {
        $resources = $this->get($client, 'boilers');

        foreach ($resources as $resource) {
            if (isset($resource['brand'])) {
                Brand::withTrashed()
                    ->updateOrCreate([
                        'id' => $resource['brand']['id'],
                    ], $this->prepareBrandData($resource['brand']));
            }

            if (isset($resource['thermostats'])) {
                $thermostatsIds = [];
                foreach ($resource['thermostats'] as $thermostatData) {
                    $thermostat = Thermostat::withTrashed()
                        ->updateOrCreate([
                            'id' => $thermostatData['id'],
                        ], $this->prepareThermostatData($thermostatData));

                    foreach ($thermostat->mediaAttributes as $mediaAttribute) {
                        $this->getMedia($thermostat->$mediaAttribute);
                    }

                    $thermostatsIds[] = $thermostat->id;
                }
            }

            $boiler = Boiler::withTrashed()
                ->updateOrCreate([
                    'id' => $resource['boiler']['id'],
                ], $this->prepareBoilerData($resource['boiler']));

            if ($boiler->wasRecentlyCreated) {
                $boiler->thermostats()->sync($thermostatsIds);
            }

            foreach ($boiler->mediaAttributes as $mediaAttribute) {
                $this->getMedia($boiler->$mediaAttribute);
            }
        }
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function syncBrands(Client $client): void
    {
        $resources = $this->get($client, 'brands');

        foreach ($resources as $resource) {
            $brand = Brand::withTrashed()
                ->updateOrCreate([
                    'id' => $resource['id'],
                ], $this->prepareBrandData($resource));

            foreach ($brand->mediaAttributes as $mediaAttribute) {
                $this->getMedia($brand->$mediaAttribute);
            }
        }
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function syncThermostats(Client $client): void
    {
        $resources = $this->get($client, 'thermostats');

        foreach ($resources as $resource) {
            if (isset($resource['brand'])) {
                Brand::withTrashed()
                    ->updateOrCreate([
                        'id' => $resource['brand']['id'],
                    ], $this->prepareBrandData($resource['brand']));
            }

            $thermostat = Thermostat::withTrashed()
                ->updateOrCreate([
                    'id' => $resource['thermostat']['id'],
                ], $this->prepareThermostatData($resource['thermostat']));

            foreach ($thermostat->mediaAttributes as $mediaAttribute) {
                $this->getMedia($thermostat->$mediaAttribute);
            }
        }

    }

    private function getMedia(?string $path = null): void
    {
        if (! isset($path)) {
            return;
        }

        if (! Storage::disk('media')->exists($path)) {
            $url = "https://{$this->syncWithDomain}/storage/app/media{$path}";
            $file = Http::get($url);

            if ($file->failed()) {
                return;
            }

            Storage::disk('media')->put($path, $file->body());
        }
    }

    private function prepareBoilerData(array $boilerData): array
    {
        foreach (Boiler::$syncIgnoredAttributes as $attribute) {
            if (array_key_exists($attribute, $boilerData)) {
                unset($boilerData[$attribute]);
            }
        }

        return $boilerData;
    }

    private function prepareBrandData(array $brandData): array
    {
        foreach (Brand::$syncIgnoredAttributes as $attribute) {
            if (array_key_exists($attribute, $brandData)) {
                unset($brandData[$attribute]);
            }
        }

        return $brandData;
    }

    private function prepareThermostatData(array $thermostatData): array
    {
        foreach (Thermostat::$syncIgnoredAttributes as $attribute) {
            if (array_key_exists($attribute, $thermostatData)) {
                unset($thermostatData[$attribute]);
            }
        }

        return $thermostatData;
    }
}
