[CareersVacancyList]

[siteSearchInclude]
==
{% set vacancies = CareersVacancyList.vacancies %}
{% set careersPage = CareersVacancyList.careersPage %}
{% set vacancyPage = CareersVacancyList.vacancyPage %}

<div class="" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="lg:grid lg:grid-cols-3 lg:gap-8">

            <div class="max-w-xl">
                <h3 class="text-base font-semibold tracking-wide text-primary-600 uppercase">{{ 'Werken bij'|_ }} {{ this.theme.company.name }}</h3>
                <h2 class="mt-2 text-2xl font-extrabold text-gray-900 sm:text-3xl lg:text-4xl">{{ 'Onze vacatures'|_ }}</h2>
            </div>

            <div class="col-span-2">
                <div class="bg-white shadow overflow-hidden sm:rounded-md">
                    <ul role="list" class="divide-y divide-gray-200">
                        {% for item in vacancies %}
                        <li>
                            <a href="{{ vacancyPage | page({ slug: item.slug }) }}" class="block hover:bg-gray-50">
                                <div class="px-4 py-4 sm:px-6">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-primary-600 truncate">{{ item.title }}</p>
                                        {% if item.hours %}
                                            <div class="ml-2 flex-shrink-0 items-center">
                                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    <i class="my-auto mr-1.5 fa-solid fa-clock"></i>{{ item.hours }}</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="mt-2 sm:flex sm:justify-between">
                                        <div class="sm:flex">
                                            <p class="flex items-center text-sm text-gray-500">
                                                <i class="fa-solid fa-list-check flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"></i>
                                                {{ item.main_task }}
                                            </p>
                                            {% if item.location %}
                                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                <i class="fa-solid fa-location-dot flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"></i>
                                                {{ item.location }}
                                            </p>
                                            {% endif %}
                                        </div>
                                        {% if item.start_date %}
                                        <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                            <i class="flex-shrink-0 mr-1.5 h-5 w-5 fa-solid fa-calendar-days text-gray-400"></i>
                                            <p>
                                                Per
                                                <time datetime="{{ item.start_date|date('m-d-Y') }}">{{ item.start_date|date('F, Y')|lower }}</time>
                                            </p>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>

        </div>
    </div>
</div>
