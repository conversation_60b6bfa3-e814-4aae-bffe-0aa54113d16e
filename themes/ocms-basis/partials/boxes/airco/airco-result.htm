{% if this.request.method == 'POST' %}
    {% set width = input('width') %}
    {% set length = input('length') %}
    {% set height = input('height') %}
    {% set insulation = input('insulation') %}
    {% set volume = width * length * height %}
    {% set factor_total = volume * insulation %}
    {% set capacity = factor_total / 1000 %}
    {% set capacity = capacity|round(1) %}
    {% set insulation_label = "Goed" %}
    {% if insulation == "40" %}
        {% set insulation_label = "Gemiddeld" %}
    {% elseif insulation == "50" %}
        {% set insulation_label = "Slecht" %}
    {% endif %}
{% endif %}

<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="airco-result"
    data-category="airco"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">
    <div class="container">

        <div class="flex flex-col gap-8 md:grid md:grid-cols-12">

            <div class="{{ this.request.method == 'POST' ? 'md:col-span-4' : 'md:col-span-6' }} space-y-8">
                {% if box.title or box.subtitle or box.content %}
                    {% partial 'atomic/molecules/prose/prose-primary' body %}
                        {% partial 'atomic/molecules/content-heading' %}
                        {% partial 'atomic/molecules/content-section' content=box.content %}
                    {% endpartial %}

                    {% if box.buttons %}
                        {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap" %}
                    {% endif %}
                {% endif %}
            </div>

            <div class="{{ this.request.method == 'POST' ? 'md:col-span-8' : 'md:col-span-6' }} space-y-8">
                <div class="p-4 md:p-8 xl:p-10 bg-gray-100 rounded-xl">
                    {% if this.request.method == 'POST' %}
                    <div class="text-xl md:text-2xl lg:text-3xl font-bold">Uw aanbevolen capaciteit: <span class="text-primary-500 font-extrabold">{{ capacity }}kW</span></div>
                    <div class="flex gap-4 items-center mt-2">
                        <div class="py-1 px-2 rounded border bg-white text-sm">Lengte: {{ length }}m</div>
                        <div class="py-1 px-2 rounded border bg-white text-sm">Breedte: {{ width }}m</div>
                        <div class="py-1 px-2 rounded border bg-white text-sm">Hoogte: {{ height }}m</div>
                        <div class="py-1 px-2 rounded border bg-white text-sm">Isolatie: {{ insulation_label }}</div>
                    </div>

                    <div class="mt-8">
                        <div class="prose prose-primary max-w-none">
                            <h3>Geschikte airco's</h3>
                        </div>

                        <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-2 md:gap-4 mt-4">
                            {% set filtered_aircos = aircos.where('capacity', '>', capacity) %}
                            {% for item in filtered_aircos %}
                                {% set price = format.euro(item.price) %}
                                <a href="{{ aircoPage | page({ slug: item.slug }) }}" class="flex flex-col p-4 rounded-md border bg-white">
                                    <div class="relative">
                                        <div class="aspect-thumb w-full">
                                            {% for img in item.images|slice(0,1) %}
                                                <img src="{{ img | media }}" alt="" class="w-full h-full object-cover">
                                            {% endfor %}
                                        </div>
                                        <div class="absolute top-2 right-2 flex gap-2">
                                            {% if item.installation_included %}
                                                <div class="py-1.5 px-3 text-lg font-semibold bg-secondary text-white rounded-md">{{ 'Incl. montage'|_ }}</div>
                                            {% endif %}
                                            <div class="py-1.5 px-3 text-lg font-semibold bg-primary text-white rounded-md">{{ item.capacity }}kW</div>
                                        </div>
                                    </div>
                                    <div class="text-xl font-bold mt-3">{{ item.title }}</div>
                                    <div class="flex items-center gap-2 mt-2">
                                        <div class="py-1.5 text-lg font-semibold rounded-md whitespace-nowrap {{ item.price_new ? 'line-through text-gray-400' : 'text-primary' }}">&euro; {{ item.price | number_format(2, ',', '.') }}</div>
                                        {% if item.price_new %}
                                            <div class="py-1.5 text-lg font-semibold text-primary rounded-md whitespace-nowrap">&euro; {{ item.price_new | number_format(2, ',', '.') }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="mt-auto pt-3">
                                        <button type="button" class="btn btn-filled btn-primary btn-rounded w-full justify-center">Meer informatie</button>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                    {% else %}
                        <div class="prose prose-primary max-w-none">
                            <h3>{{ box.form_title }}</h3>
                            {{ box.form_text | content }}
                        </div>

                        {% partial 'plugins/airco/calculator' %}
                    {% endif %}
                </div>
            </div>

        </div>

    </div>
</section>
