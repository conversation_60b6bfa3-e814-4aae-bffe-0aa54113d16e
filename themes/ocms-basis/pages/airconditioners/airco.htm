url = "/airco/:slug"
layout = "default"
title = "Blogbericht"

[AircoDetail]
slug = "{{ :slug }}"

[aircoList]
==
{% if not AircoDetail %}
    {% do abort(404) %}
{% endif %}

{% set airco = AircoDetail.airco %}

<section class="py-8 md:py-16" x-data="openClose">
    <div class="container">
        <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-12 md:gap-16">
            <div class="md:col-span-6">
                <div class="aspect-thumb flex items-center justify-center">
                    <img src="{{ airco.images.0|media|resize(800) }}" alt="{{ airco.title }}" class="w-full h-full object-cover">
                </div>
            </div>
            <div class="md:col-span-6 space-y-6">
                <div class="prose prose-primary">
                    <h1>{{ airco.title }}</h1>
                </div>
                <div class="flex items-center gap-2 mt-2">
                    <div class="py-1.5 text-2xl font-semibold rounded-md whitespace-nowrap {{ airco.price_new ? 'line-through text-gray-400' : 'text-primary' }}">&euro; {{ airco.price | number_format(2, ',', '.') }}</div>
                    {% if airco.price_new %}
                        <div class="py-1.5 text-2xl font-semibold text-primary rounded-md whitespace-nowrap">&euro; {{ airco.price_new | number_format(2, ',', '.') }}</div>
                    {% endif %}
                </div>
                <div class="prose prose-primary">
                    {{ airco.description | content }}
                </div>
                <button type="button" class="flex items-center justify-center rounded-md border border-transparent bg-primary-500 py-3 px-8 text-base font-medium text-white hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50" @click="toggle">{{ 'Offerte aanvragen'|_ }}</button>

                {% partial 'plugins/airco/offer' %}
            </div>
        </div>
        <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-12 md:gap-16 mt-6">
            <div class="md:col-span-6">
                <div class="prose prose-primary max-w-none">
                    <h2>Specificaties</h2>
                    <div class="flex flex-col">
                        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8 -mt-4">
                            <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <tbody class="divide-y divide-gray-200">
                                        {% for item in airco.specifications %}
                                            <tr>
                                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ item.label }}</td>
                                            <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ item.value }}</td>
                                        </tr>
                                        {% endfor %}

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% if airco.brochure or airco.manual or airco.technical_manual %}
            <div class="md:col-span-6">
                <div class="prose prose-primary max-w-none">
                    <h2>Documentatie</h2>
                    <div class="flex flex-col">
                        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8 -mt-4">
                            <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <tbody class="divide-y divide-gray-200">
                                        {% if airco.brochure %}
                                            <tr>
                                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">Brochure</td>
                                                <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">
                                                    <a href="{{ airco.brochure|media }}" target="_blank">Download brochure <i class="fa-regular fa-arrow-right ml-2"></i></a>
                                                </td>
                                            </tr>
                                        {% endif %}
                                        {% if airco.manual %}
                                            <tr>
                                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">Handleiding</td>
                                                <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">
                                                    <a href="{{ airco.manual|media }}" target="_blank">Download handleiding <i class="fa-regular fa-arrow-right ml-2"></i></a>
                                                </td>
                                            </tr>
                                        {% endif %}
                                        {% if airco.technical_manual %}
                                            <tr>
                                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">Technische handleiding</td>
                                                <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">
                                                    <a href="{{ airco.technical_manual|media }}" target="_blank">Download technische handleiding <i class="fa-regular fa-arrow-right ml-2"></i></a>
                                                </td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</section>
