url = "/nieuws/bericht/:slug"
layout = "default"
title = "Nieuwsbericht"

[postDetail]
slug = "{{ :slug }}"

[postList blogItems3]
maxItems = 3
sorting = "publication_date"
sortDirection = "desc"
hidePost = "{{ :slug }}"

[categoryList]
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $post = $this->components['postDetail'];
        $postTitle = $post->post?->title;

        if (
            isset($post->post?->seo['meta_title'])
            &&
            trim($post->post?->seo['meta_title']) !== ''
        ) {
            $seoTitle = $post->post?->seo['meta_title'];
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $postTitle;
        $this->page->title = $title . $seoTitleSuffix;
    }

?>
==

{% if not postDetail %}
    {% do abort(404) %}
{% endif %}

<article class="blog-post-page">
    <header class="hidden md:block h-[600px] absolute inset-x-0 top-0 bg-gradient-to-b from-gray-200"></header>

    <div class="container relative z-20 mt-16">
        <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-3 md:gap-8">
            <section class="blog-post-content col-span-2 p-8 bg-white rounded-lg prose prose-primary dark:prose-primary_inverted max-w-none">
                <h1>{{ post.title }}</h1>
                <div class="border-t border-b py-3 flex gap-x-8">
                    {% if postDetail.showDates %}
                        <div>
                            <time title="{{ post.publication_date | date('j F, Y, H:i') }}">{{ post.publication_date | date('d M Y') }}</time>
                        </div>
                    {% endif %}
                    <div class="mr-auto">
                        {% for cat in post.categories %}
                            <a href="{{ categoryPage | page({ slug: cat.slug }) }}">{{ cat.title }}</a>{{ not loop.last ? ', ' }}
                        {% endfor %}
                    </div>
                    {% if postDetail.showAuthor and post.author %}
                        <div>
                            by {{ post.author.first_name }} {{ post.author.last_name }}
                        </div>
                    {% endif %}
                </div>
                <div class="mt-6">
                    <div class="float-right aspect-thumb md:w-1/3 rounded-md overflow-hidden border ml-4"><img src="{{ post.image | media | resize(800, 600) }}" alt="" class="w-full h-full object-cover mt-0"></div>
                    {% if post.excerpt %}
                        <p class="lead">{{ post.excerpt }}</p>
                    {% endif %}

                    {{ post.content | content }}
                </div>
                <div class="mt-6 pt-6 border-t not-prose">
                    <a href="{{ blogPage | link }}" class="btn btn-grayscale btn-outlined btn-pill btn-sm">Terug naar overzicht</a>
                </div>
            </section>
            <aside class="blog-post-aside">
                {% component 'categoryList' %}
            </aside>
        </div>
    </div>
</article>

{% set items = blogItems3.posts %}

<section id="postlist-1" class="py-8 md:py-16 lg:py-24 bg-gray-100">
    <div class="container">
        <div class="prose prose-primary dark:prose-primary_inverted max-w-none text-center">
            <h2>{{ 'Laatste nieuwsberichten'|_ }}</h2>
        </div>
        <div class="row-grid md:grid-cols-3 md:gap-8 mt-16">
            {% for item in items %}
                {% if item.slug != slug %}
                    {% partial 'atomic/molecules/cards/card-blog' blogPage=blogPage postPage=postPage categoryPage=categoryPage item=item %}
                {% endif %}
            {% endfor %}
        </div>
    </div>
</section>

{# Add SEO meta tags to page #}
{% set seo_object = post %}
{% partial 'site/seo_meta' item = seo_object %}
