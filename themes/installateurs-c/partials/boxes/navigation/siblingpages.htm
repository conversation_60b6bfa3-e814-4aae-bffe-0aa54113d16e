{% set siblings = boxesPage.getSiblingsAndSelf() %}
{% set parent = boxesPage.getParent().first() %}

{% if box.columns == '4' %}
    {% set cols = 'lg:grid-cols-4' %}
{% elseif box.columns == '3' %}
    {% set cols = 'lg:grid-cols-3' %}
{% elseif box.columns == '2' %}
    {% set cols = 'lg:grid-cols-2' %}
{% else %}
    {% set cols = 'lg:grid-cols-4' %}
{% endif %}

<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="childpages"
    data-category="navigation"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container space-y-12">
        {% if box.title or box.subtitle or box.content %}
            <div class="">
                {% partial 'atomic/molecules/content-heading-centered' box=box %}
            </div>
        {% endif %}

        <div class="space-y-6 md:space-y-0 md:grid md:grid-cols-2 {{ cols }} md:gap-8">
            {% for item in siblings %}
                {% if box.style == 'cards' %}
                    {% partial 'atomic/molecules/cards/card-page' title=item.name url=item.url image=item.custom_config.thumbnail content=item.custom_config.excerpt cardClass="bg-white dark:bg-gray-500 border rounded" %}
                {% else %}
                    {% partial 'boxes/navigation/includes/item-btn' item=item %}
                {% endif %}
            {% endfor %}
        </div>
    </div>
</section>
