handle: sidebar-style
mixin:
    _colors:
        label: Kleuren
        type: section
    background_color:
        label: Achtergrond kleur
        type: colorpicker
        availableColors: [ "transparent","#ffffff","#f3f4f6","#d1d5db","#6b7280", "#374151", "#18181b" ]
        default: "transparent"
        allowCustom: true
        allowEmpty: true
    bg_opacity:
        label: Achtergrond transparantie
        comment: "0 is doorzichtig, 100 is ondoorzichtig"
        type: number
        default: 100
        min: 0
        max: 100
        steps: 10
        span: left
    text_color:
        mixin: text-color
        type: mixin
    _effects:
        label: Overig
        type: section
    rounded_corners:
        label: Afgeronde hoeken
        type: switch
    shadow:
        label: Schaduw
        type: switch
    corner:
        label: Randen
        type: switch
    corner_width:
        label: Rand dikte
        type: dropdown
        options:
            1: 1 pixel
            2: 2 pixels
            3: 3 pixels
        default: 1
        trigger:
            action: show
            field: corner
            condition: checked
    corner_color:
        label: Rand kleur
        type: colorpicker
        availableColors: [ "transparent","#ffffff","#f3f4f6","#d1d5db","#6b7280", "#374151", "#18181b" ]
        default: "transparent"
        allowCustom: true
        allowEmpty: true
        trigger:
            action: show
            field: corner
            condition: checked
