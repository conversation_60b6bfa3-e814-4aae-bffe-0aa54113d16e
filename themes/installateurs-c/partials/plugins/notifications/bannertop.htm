{% set item = bannertop.notification %}
{% set current_date = date() %}
{% set start_date = date(item.start_date) %}
{% set end_date = date(item.end_date) %}
{% set is_active = item.is_active %}
{% set type = item.type %}
{% set id = item.id %}
{% set button = item.button %}

{% set textcolor = "text-white" %}

{% if item.style == "primary" %}
{% set maincolor = "bg-primary-500" %}
{% set darkcolor = "bg-primary-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "secondary" %}
{% set maincolor = "bg-secondary-500" %}
{% set darkcolor = "bg-secondary-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "success" %}
{% set maincolor = "bg-green-500" %}
{% set darkcolor = "bg-green-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "warning" %}
{% set maincolor = "bg-amber-500" %}
{% set darkcolor = "bg-amber-600" %}
{% set iconType = "circle-exclamation" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "danger" %}
{% set maincolor = "bg-red-500" %}
{% set darkcolor = "bg-red-600" %}
{% set iconType = "circle-xmark" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "info" %}
{% set maincolor = "bg-sky-500" %}
{% set darkcolor = "bg-sky-600" %}
{% set iconType = "circle-info" %}
{% set textcolor = "text-white" %}

{% endif %}

{% if item %}

<div class="{{ maincolor }}" id="notificationTop_{{ id }}" style="z-index: 99; display: none;">
    <div class="container">
        <div class="flex items-center justify-between gap-x-8 py-4">
            <div class="flex-1">
                <p class="{{ textcolor }} text-base md:text-lg font-bold">
                    {{ item.title }}
                </p>
            </div>
            {% if button %}
                <div class="">
                    {% for button in button %}
                        <div class="">
                            {% partial 'ui/button' item = button %}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            <div class="">
                <button id="notificationTopClose" type="button" class="-p-1 focus-visible:outline-offset-[-4px]  opacity-70 hover:opacity-100">
                    <span class="sr-only">{{ 'Dismiss'|_ }}</span>
                    <i class="fa-solid fa-xmark {{ textcolor }}"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}
