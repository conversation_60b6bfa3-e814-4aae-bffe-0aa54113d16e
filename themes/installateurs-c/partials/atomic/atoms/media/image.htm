{# Resize option vars: width, height, resize mode, extension, quality #}
{% set w = resize_w|default('auto') %}
{% set h = resize_h|default('auto') %}
{% set m = resize_mode|default('auto') %}
{% set x = resize_ext|default('webp') %}
{% set q = resize_ql|default(90) %}

{% if img %}
    {% if external %}
        <img src="{{ img }}" alt="{{ title }}" class="{{ class }}">
    {% else %}
        <img src="{{ img | media | resize(w, h, { filename: true, mode: m, extension: x, quality: q }) }}" alt="{{ title }}" class="{{ class }} {{ objectAlign }} {{ shadow ? 'relative z-20' }}">
    {% endif %}
{% endif %}
