{% if autoplay %}
    {% set muted = true %}
{% endif %}

{% if video %}
    <div class="alpine-video-player relative inline-block {{ wrapClass }}" x-data="{ play: {{ autoplay ? 'true' : 'false' }} }" x-init="$watch('play', (value) => {
        if (value) {
            $refs.video.play()
        } else {
            $refs.video.pause()
        }
    })">
        <video {% if width %}width="{{ width }}"{% endif %} {% if height %}height="{{ height }}"{% endif %} src="{{ video | media }}" {{ loop ? 'loop' }} {{ autoplay ? 'autoplay' }} {{ muted ? 'muted' }} preload="metadata" class="{{ class }} relative" x-ref="video" @click="play = !play"></video>

        <div class="absolute inset-0 flex items-center justify-center cursor-pointer z-20" x-show="!play" @click="play = true" x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-90" {% if autoplay %}x-cloak{% endif %}>
            <svg class="h-20 w-20 text-primary-500 cursor-pointer group hover:text-primary-600 transition" fill="currentColor" viewBox="0 0 84 84">
                <circle cx="42" cy="42" r="42" fill="white" class="opacity-60 group-hover:opacity-80 transition"></circle>
                <path d="M55.5039 40.3359L37.1094 28.0729C35.7803 27.1869 34 28.1396 34 29.737V54.263C34 55.8604 35.7803 56.8131 37.1094 55.9271L55.5038 43.6641C56.6913 42.8725 56.6913 41.1275 55.5039 40.3359Z"></path>
            </svg>
        </div>
    </div>
{% endif %}
