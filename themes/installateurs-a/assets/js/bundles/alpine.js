// Import AlpineJS
import Alpine from 'alpinejs';
import AlpineRayPlugin from 'alpinejs-ray';

// Import AlpineJS Plugins
import intersect from '@alpinejs/intersect'
import collapse from '@alpinejs/collapse'
import mask from '@alpinejs/mask'
import focus from '@alpinejs/focus'
import persist from '@alpinejs/persist'

// Import AlpineJS Data
import tabs from './../components/alpine/tabs';
// import tooltip from './../components/alpine/tooltip';
import openClose from './../components/alpine/openClose';
import searchBox from './../components/alpine/searchBox';
import radioButtonGroup from './../components/alpine/radioButtonGroup';
import setPersist from './../components/alpine/persist';
import cookieTabs from './../components/alpine/cookieTabs';

// Axios
window.axios = require('axios');
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Activate AlpineJS Plugins
Alpine.plugin(focus)
Alpine.plugin(intersect)
Alpine.plugin(collapse)
Alpine.plugin(mask)
Alpine.plugin(persist)

// Activate AlpineJS Data
Alpine.data('openClose', openClose)
Alpine.data('tabs', tabs)
// Alpine.data('tooltip', tooltip)
Alpine.data('searchBox', searchBox)
Alpine.data('radioButtonGroup', radioButtonGroup)
Alpine.data('setPersist', setPersist)
Alpine.data('cookieTabs', cookieTabs)

// Start AlpineJS
window.Alpine = Alpine;

Alpine.plugin(AlpineRayPlugin);
Alpine.start();
