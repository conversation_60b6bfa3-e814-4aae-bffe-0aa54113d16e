/* New */

/* Fieldset */
.form-fieldset-inner { @apply bg-white rounded-2xl shadow-xl p-4 md:p-8 xl:p-10; }

/* Form fields wrapper */
.form-wrapper { @apply flex flex-wrap -mx-4; }
.form-fieldset-flex { @apply flex flex-wrap -mx-4; }
.form-wrapper > div { @apply w-full px-4 mb-4; }
.form-fieldset-flex > .form-fieldset-heading { @apply w-full px-4 mb-4; }
.form-fieldset-flex > div { @apply w-full px-4 mb-4; }
.form-fieldset-buttons { @apply px-4 mb-4; }
.form-wrapper > .col-md-12 { @apply md:w-full; }
.form-wrapper > .col-md-11 { @apply md:w-11/12; }
.form-wrapper > .col-md-10 { @apply md:w-10/12; }
.form-wrapper > .col-md-9 { @apply md:w-9/12; }
.form-wrapper > .col-md-8 { @apply md:w-8/12; }
.form-wrapper > .col-md-7 { @apply md:w-7/12; }
.form-wrapper > .col-md-6 { @apply md:w-6/12; }
.form-wrapper > .col-md-5 { @apply md:w-5/12; }
.form-wrapper > .col-md-4 { @apply md:w-4/12; }
.form-wrapper > .col-md-3 { @apply md:w-3/12; }
.form-wrapper > .col-md-2 { @apply md:w-2/12; }
.form-wrapper > .col-md-1 { @apply md:w-1/12; }

.form-fieldset-flex > .col-md-12 { @apply md:w-full; }
.form-fieldset-flex > .col-md-11 { @apply md:w-11/12; }
.form-fieldset-flex > .col-md-10 { @apply md:w-10/12; }
.form-fieldset-flex > .col-md-9 { @apply md:w-9/12; }
.form-fieldset-flex > .col-md-8 { @apply md:w-8/12; }
.form-fieldset-flex > .col-md-7 { @apply md:w-7/12; }
.form-fieldset-flex > .col-md-6 { @apply md:w-6/12; }
.form-fieldset-flex > .col-md-5 { @apply md:w-5/12; }
.form-fieldset-flex > .col-md-4 { @apply md:w-4/12; }
.form-fieldset-flex > .col-md-3 { @apply md:w-3/12; }
.form-fieldset-flex > .col-md-2 { @apply md:w-2/12; }
.form-fieldset-flex > .col-md-1 { @apply md:w-1/12; }

/* Field wrapper */
.form-field-wrapper {
    @apply mb-6 relative w-full px-4 lg:w-1/2;
}
.form-field-wrapper.row-1-2,
.form-field-wrapper.row-2-4 { @apply md:w-1/2; }
.form-field-wrapper.row-1-3 { @apply md:w-1/3; }
.form-field-wrapper.row-2-3 { @apply md:w-2/3; }
.form-field-wrapper.row-1-4 { @apply md:w-1/4; }
.form-field-wrapper.row-3-4 { @apply md:w-3/4; }


/* Form Label */
.form-wrapper .form-label { @apply mb-2 inline-block text-gray-500 font-bold; }
.dark .form-wrapper .form-label { @apply text-white font-semibold; }
.form-wrapper .form-text { @apply text-sm text-gray-400 mt-1; }

/* Form Input & Select */
.form-wrapper .form-control,
.form-wrapper .form-select { @apply block w-full px-3 py-2.5 border border-gray-300 rounded-md ring-primary-300 focus:ring-2 focus:border-primary-300 bg-white; }
.dark .form-wrapper .form-control,
.dark .form-wrapper .form-select { @apply block w-full px-3 py-2.5 border border-gray-300 rounded-md ring-primary-600 focus:ring-2 focus:border-primary-600 bg-white; }

/* Form Checkbox */
.form-wrapper .form-check { @apply flex flex-wrap items-center gap-x-3; }
.form-wrapper .form-check .form-check-input { @apply rounded border-gray-300 text-primary-500 focus:ring-primary-300 ring-offset-1 focus:border-primary-300 checked:border-primary-500; }
.form-wrapper .form-check input[type="radio"].form-check-input { @apply rounded-full; }

/* Form Upload */
.form-wrapper .form-control::file-selector-button { @apply py-1.5 px-3 -my-2.5 -ml-3 border-0 border-r border-solid border-gray-300 appearance-none shadow-none mr-2 transition bg-gray-200; }
.form-wrapper .form-control:hover::file-selector-button { @apply bg-gray-300; }


/* Submit */
.form-wrapper .btn-primary { @apply bg-primary-500 hover:bg-primary-600 rounded-full text-white;}

/* Validation */
.form-wrapper .form-control.is-invalid { @apply border-red-500 focus:ring-red-300 pr-6 bg-no-repeat; }
.form-wrapper .form-control.is-invalid {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-wrapper textarea.form-control.is-invalid {
    background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.form-wrapper .invalid-feedback { @apply text-sm font-medium text-red-500 mt-1; }
.form-wrapper .form-check .invalid-feedback { @apply w-full; }

/* Floating Labels */

.form-floating { @apply relative; }

.form-floating > input.form-control,
.form-floating > textarea.form-control { @apply py-4 px-3 transition-all; }

.form-floating > label { @apply absolute left-3 top-4 select-none transition-all; }

.form-floating > input.form-control:focus,
.form-floating > textarea.form-control:focus,
.form-floating > input.form-control:not(:placeholder-shown),
.form-floating > textarea.form-control:not(:placeholder-shown),
.form-floating > .form-select { @apply pt-6 pb-2; }

.form-floating > input.form-control:focus ~ label,
.form-floating > textarea.form-control:focus ~ label,
.form-floating > input.form-control:not(:placeholder-shown) ~ label,
.form-floating > textarea.form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label { @apply text-gray-500 text-xs top-2.5; }

.form-horizontal .form-wrapper > div,
.form-horizontal .form-wrapper .form-button-wrap {
    @apply md:flex md:flex-wrap;
}
.form-horizontal .form-wrapper .form-label,
.form-horizontal .form-wrapper .form-button-wrap .form-empty-space { @apply w-full md:w-3/12 md:text-right md:pr-4 relative top-2; }
.form-horizontal .form-wrapper .form-control,
.form-horizontal .form-wrapper .form-select,
.form-horizontal .form-wrapper .form-button-wrap .form-button { @apply w-full md:w-9/12; }
.form-horizontal .form-wrapper .invalid-feedback,
.form-horizontal .form-wrapper .form-text { @apply md:w-full md:text-right; }

.form-field-wrapper.row-1-2,
.form-field-wrapper.row-2-4 { @apply md:w-1/2; }
.form-field-wrapper.row-1-3 { @apply md:w-1/3; }
.form-field-wrapper.row-2-3 { @apply md:w-2/3; }
.form-field-wrapper.row-1-4 { @apply md:w-1/4; }
.form-field-wrapper.row-3-4 { @apply md:w-3/4; }

.upload-field-wrap { @apply flex -mx-2; }
.upload-field-wrap > div { @apply px-2; }
.upload-field-wrap ul.upload-file-list { @apply empty:hidden mt-2 border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200; }
.upload-field-wrap ul.upload-file-list .uploaded-file { @apply py-1.5 px-2 text-sm; }
