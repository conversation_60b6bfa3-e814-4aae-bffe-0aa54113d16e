.fotogalerij-slider-wrapper { @apply flex flex-wrap justify-between md:grid md:grid-cols-12; }
.foto-slider-buttons { @apply flex items-center justify-center; }
.foto-slider-buttons .foto-slider-button { @apply cursor-pointer flex items-center justify-center transition select-none; }
.foto-slider-buttons-prev { @apply order-2 md:order-none px-4 md:px-0; }
.foto-slider-buttons-next { @apply order-3 md:order-none px-4 md:px-0; }

.foto-slider-wrapper { @apply w-full md:w-auto md:col-span-10; }
.foto-slider { @apply max-w-full w-full order-1 md:order-none; }
.foto-slider .feed-item-outer { @apply p-4 pb-6; }
.foto-slider .feed-item-inner { @apply bg-white border shadow-lg overflow-hidden; }
.foto-slider .feed-item-thumb { @apply block aspect-thumb w-full relative; }

.foto-slider .feed-item-thumb .foto-watermark { @apply w-8 absolute top-4 right-4; }
.foto-slider .feed-item-content { @apply p-4; }
.foto-slider .feed-item-caption { @apply pb-4 border-b mb-4; }
.foto-slider .feed-item-details { @apply flex gap-8 items-center justify-between; }
.foto-slider .feed-item-details .feed-item-details-profile { @apply font-medium text-primary-500 hover:text-primary-700; }
.foto-slider .feed-item-details .feed-item-details-date { @apply text-sm; }
