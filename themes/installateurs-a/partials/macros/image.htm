{% macro dynamicimage(img, sm, md, lg, xl, xxl) %}
    {% set imgsrc = img %}
    {% set srcset = [] %}
    {% set sizes = [] %}

    {% if sm %}
      {% set imgsm = imgsrc|resize(sm) %}
      {% set srcset = srcset|merge([imgsm ~ " " ~ sm ~ "w"]) %}

      {% if md %}
        {% set sizes = sizes|merge(["(max-width: " ~ sm ~ "px) " ~ sm ~ "px"]) %}
      {% else %}
        {% set sizes = sizes|merge([", " ~ sm ~ "px"]) %}
      {% endif %}
    {% endif %}

    {% if md %}
      {% set imgmd = imgsrc|resize(md) %}
      {% set srcset = srcset|merge([imgmd ~ " " ~ md ~ "w"]) %}
      
      {% if lg %}
        {% set sizes = sizes|merge(["(max-width: " ~ md ~ "px) " ~ md ~ "px"]) %}
      {% else %}
        {% set sizes = sizes|merge([md ~ "px"]) %}
      {% endif %}
    {% endif %}

    {% if lg %}
      {% set imglg = imgsrc|resize(lg) %}
      {% set srcset = srcset|merge([imglg ~ " " ~ lg ~ "w"]) %}

      {% if xl %}
        {% set sizes = sizes|merge(["(max-width: " ~ lg ~ "px) " ~ lg ~ "px"]) %}
      {% else %}
        {% set sizes = sizes|merge([lg ~ "px"]) %}
      {% endif %}
    {% endif %}

    {% if xl %}
      {% set imgxl = imgsrc|resize(xl) %}
      {% set srcset = srcset|merge([imgxl ~ " " ~ xl ~ "w"]) %}

      {% if xxl %}
        {% set sizes = sizes|merge(["(max-width: " ~ xl ~ "px) " ~ xl ~ "px"]) %}
      {% else %}
        {% set sizes = sizes|merge([xl ~ "px"]) %}
      {% endif %}
    {% endif %}
    {% if xxl %}
      {% set imgxxl = imgsrc|resize(xxl) %}
      {% set srcset = srcset|merge([imgxxl ~ " " ~ xxl ~ "w"]) %}
      {% set sizes = sizes|merge([xxl ~ "px"]) %}
    {% endif %}


    <img srcset="{{ srcset|join(', ') }}" sizes="{{ sizes|join(', ') }}" src="{{ xxl ? imgxxl : xl ? imgxl : lg ? imglg : md ? imgmd : imgsm }}" alt="" />
{% endmacro %}


{# Importeer de macro functie #}
{# {% import 'macros/image' as image %} #}

{# Definieer de afbeelding variabel #}
{# {% set img = 'assets/images/foto.jpg'|theme %} #}

{# 
    Roep de functie aan: dynamicimage() met de parameters:
    1: afbeelding variabel met url naar afb.
    2: kleinste formaat van de afbeelding in pixels (verplicht)
    3: md formaat van de afbeelding in pixels (verplicht)
    4: lg formaat van de afbeelding in pixels (niet verplicht)
    5: xl formaat van de afbeelding in pixels (niet verplicht)
    6: xxl formaat van de afbeelding in pixels (niet verplicht)
#}
{# {{ image.dynamicimage(img, 640, 786, 1024, 1280, 1536) }} #}

