{% set item = bannerbottom.notification %}
{% set current_date = date() %}
{% set start_date = date(item.start_date) %}
{% set end_date = date(item.end_date) %}
{% set is_active = item.is_active %}
{% set type = item.type %}
{% set id = item.id %}
{% set button = item.button %}

{% if item.style == "primary" %}
{% set maincolor = "bg-primary-500" %}
{% set darkcolor = "bg-primary-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "secondary" %}
{% set maincolor = "bg-secondary-500" %}
{% set darkcolor = "bg-secondary-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "success" %}
{% set maincolor = "bg-green-500" %}
{% set darkcolor = "bg-green-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "warning" %}
{% set maincolor = "bg-amber-500" %}
{% set darkcolor = "bg-amber-600" %}
{% set iconType = "circle-exclamation" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "danger" %}
{% set maincolor = "bg-red-500" %}
{% set darkcolor = "bg-red-600" %}
{% set iconType = "circle-xmark" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "info" %}
{% set maincolor = "bg-sky-500" %}
{% set darkcolor = "bg-sky-600" %}
{% set iconType = "circle-info" %}
{% set textcolor = "text-white" %}

{% endif %}

{% if item %}

<div class="fixed inset-x-0 bottom-0 sm:px-6 sm:pb-6 lg:px-8 lg:pb-12" id="notificationBottom_{{ id }}" style="z-index: 99; display: none;">
    <div class="container">
        <div class="">
            <div class="{{ maincolor }} rounded-lg shadow-2xl">
                <div class="p-3 md:p-6">
                    <div class="flex items-start w-full">
                        <span class="hidden md:flex rounded-lg {{ darkcolor }} p-2">
                            <i class="fa-regular fa-{{ iconType }} {{ textcolor }} w-10 h-10"></i>
                        </span>
                        <div class="font-medium md:px-4 xl:px-8 w-full">
                            {% if item.title %}
                            <div class="{{ textcolor }} uppercase text-base md:text-lg font-bold mb-3">{{ item.title }}
                            </div>
                            {% endif %}
                            <div class="flex flex-col space-y-4 md:space-y-0 md:space-x-4 md:flex-row md:items-center md:justify-between xl:space-x-8">
                                <div class="{{ textcolor }}">{{ item.notification|content }}</div>

                                {% if button %}
                                <div class="pt-4 md:pt-0">
                                    {% for button in button %}
                                    <div class="">
                                        {% partial 'ui/button' item = button %}
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div>
                            <button id="notificationBottomClose" type="button" class="-p-1 focus-visible:outline-offset-[-4px]">
                                <span class="sr-only">{{ 'Dismiss'|_ }}</span>
                                <i class="fa-solid fa-xmark {{ textcolor }}"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
