<section
    data-name="text-image-overlap"
    data-category="text"
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }} "
    data-boxes-container
    data-rel="boxes-wrapper">

    <div style="background-color: {{ box.background_color }};" class="py-8 md:py-12 lg:py-16">
        <div class="container">
            <div class="flex flex-col gap-8 lg:gap-16 lg:grid lg:grid-cols-12">
                <div class="lg:col-span-6 space-y-8 {{ box.flip_content ? 'order-last' : 'order-last lg:order-first' }}">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% if box.title or box.subtitle %}
                            {% partial 'atomic/molecules/content-heading' %}
                        {% endif %}
                        {% if box.content %}
                            {% partial 'atomic/molecules/content-section' content=box.content %}
                        {% endif %}
                    </div>
                    {% if box.bulletpoints %}
                        {% partial 'atomic/molecules/bulletpoints' list=box.bulletpoints icon_style="ph-fill" %}
                    {% endif %}
                    {% if box.buttons %}
                        {% partial 'atomic/molecules/buttons' buttons=box.buttons %}
                    {% endif %}
                    {% if box.related_pages %}
                        {% partial 'atomic/molecules/related-pages' %}
                    {% endif %}
                </div>
                <div class="lg:col-span-6 space-y-8 relative {{ box.flip_content ? 'order-first' : 'order-first lg:order-last' }}">
                    <div class="lg:absolute lg:inset-x-0 lg:-inset-y-24 xl:-inset-y-32 aspect-thumb lg:aspect-auto -mt-8 md:-mt-24 lg:mt-0 rounded-2xl overflow-hidden">
                        {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w="610" class="rounded-2xl w-full h-full object-cover" %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
