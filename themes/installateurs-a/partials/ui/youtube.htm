[viewBag]
snippetCode = "youtubeVideo"
snippetName = "YouTube Video"
snippetDescription = "Embed a Youtube Video on the page"
snippetProperties[url][title] = "Video URL"
snippetProperties[url][type] = "string"
snippetProperties[start_at][title] = "Start At"
snippetProperties[start_at][type] = "string"
snippetProperties[privacy_enhanced][title] = "Enable privacy-enhanced mode."
snippetProperties[privacy_enhanced][type] = "checkbox"
snippetProperties[responsive_mode][title] = "Enable responsive mode."
snippetProperties[responsive_mode][type] = "checkbox"
==
// Converts https://www.youtube.com/watch?v=k_H2zJ7UZfs to k_H2zJ7UZfs
function urlToCode($link = '')
{
    $parts = parse_url($link);
    if (isset($parts['query'])) {
        parse_str($parts['query'], $qs);
        if (isset($qs['v'])){
            return $qs['v'];
        }
        elseif (isset($qs['vi'])){
            return $qs['vi'];
        }
    }
    if (isset($parts['path'])){
        $path = explode('/', trim($parts['path'], '/'));
        return $path[count($path)-1];
    }
    return null;
}

// Converts 15:00 to 900
function timeToSeconds($time = '')
{
    $parts = explode(':', $time);
    if (count($parts) === 3) {
        return $parts[0] * 3600 + $parts[1] * 60 + $parts[2];
    }
    elseif (count($parts) === 2) {
        return $parts[0] * 60 + $parts[1];
    }
    return $time ?: 0;
}
==
{% if url %}
    {% set base_url = "https://www.youtube.com" %}
    {% if privacy_enhanced %}
        {% set base_url = "https://www.youtube-nocookie.com" %}
    {% endif %}
    <div class="not-prose">
        {% if responsive_mode %}
            <iframe
                src="{{ base_url }}/embed/{{ this.urlToCode(url) }}?start={{ this.timeToSeconds(start_at) }}"
                class="aspect-video w-full"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen></iframe>
        {% else %}
            <iframe
                width="560"
                height="315"
                src="{{ base_url }}/embed/{{ this.urlToCode(url) }}?start={{ this.timeToSeconds(start_at) }}"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen></iframe>
        {% endif %}
    </div>
{% else %}
    <!-- Video URL Missing -->
{% endif %}
