<div class="openingtimes-table">
        <table class="w-full align-top">
            {% for item in openingtimes %}
            <tr>
                <th scope="col" class="text-left pb-1 font-semibold leading-none align-top">
                    {% if item.day == 1 %}
                        {{ 'Maandag'|_ }}
                    {% elseif item.day == 2 %}
                        {{ 'Dinsdag'|_ }}
                    {% elseif item.day == 3 %}
                        {{ 'Woensdag'|_ }}
                    {% elseif item.day == 4 %}
                        {{ 'Donderdag'|_ }}
                    {% elseif item.day == 5 %}
                        {{ 'Vrijdag'|_ }}
                    {% elseif item.day == 6 %}
                        {{ 'Zaterdag'|_ }}
                    {% elseif item.day == 7 %}
                        {{ 'Zondag'|_ }}
                    {% elseif item.day == 'anders' %}
                        {{ item.day_custom }}
                    {% endif %}
                </th>

                {% if item.closed %}
                    <td scope="col" class="pb-1 align-top">{{ 'Gesloten'|_ }}</td>
                {% else %}
                    <td scope="col" class="pb-1 align-top">{{ carbon(item.open).format('H:i') }} - {{ carbon(item.close).format('H:i') }}</td>
                {% endif %}
            </tr>
            {% endfor %}
        </table>
    </div>
