<a href="{{ postPage | page({ slug: item.slug }) }}"
   class="group/card flex flex gap-4 {{ class }}">
    <div class="overflow-hidden rounded-md shrink-0">
        {% partial 'atomic/atoms/media/image' img=item.thumbnail_image title=item.thumb_img_title resize_w='320' class='h-20 w-20 max-h-28 mx-auto object-cover border border-gray-400 rounded-md group-hover/card:scale-110 transition-all duration-300 p-0.5' %}
    </div>
    <div class="col-span-2 flex flex-col w-full justify-center space-y-3">
        <div class="prose prose-primary dark:prose-primary_inverted max-w-none text-sm leading-snug">
            {% if item.title_short %}
                {% set blogTitle = item.title_short %}
            {% else %}
                {% set blogTitle = item.title %}
            {% endif %}

            {% partial 'atomic/atoms/cards/card-heading' text=blogTitle level=3 class='font-bold group-hover/card:text-primary dark:group-hover/card:text-primary-highlight transition-all duration-400' %}
        </div>
        <div class="flex">
            {% if postList.showDates %}
                {% partial 'atomic/atoms/blog/date' date=item.publication_date class='text-xs' %}
            {% elseif postList.showAuthor %}
                <div class="flex items-center space-x-2">
                    {% if post.author.avatar %}
                        {% partial 'atomic/atoms/media/image' img=item.author.avatar title=item.author.first_name resize_w='28' class='h-6 w-6 rounded-full' %}
                    {% endif %}
                    {% partial 'atomic/atoms/blog/author' firstName=item.author.first_name lastName=item.author.last_name class='text-xs' %}
                </div>
            {% else %}
                <div class="ml-auto pr-2">
                    {% set text='Lees meer >' | _ %}
                    {% partial 'atomic/atoms/cards/card-heading' text=text class='text-xs' level=5 %}
                </div>
            {% endif %}
        </div>

    </div>
</a>