<dt>
    <button class="faq-question {{ reversed ? 'flex-row-reverse' }}" @click="expanded = !expanded">
        <span class="leading-7 flex-1">{{ question }}</span>
        <span class="leading-none w-5 text-secondary">
            <i class="{{ opened_icon|default('fa-duotone fa-lg fa-circle-chevron-down') }}" x-show="expanded"></i>
            <i class="{{ closed_icon|default('fa-duotone fa-lg fa-circle-chevron-right') }}" x-show="!(expanded)"></i>
        </span>
    </button>
</dt>

{#
    accepted variables:

    'question'
        - text to display
        - accept html: no
        - accept markdown: yes
        - default: null

    'reversed'
        - reverse icons and text
        - boolean

    'opened_icon'
        - icon classes for opened state
        - accept html: no
        - accept markdown: no
        - default: 'fa-duotone fa-lg fa-circle-chevron-down'

    'closed_icon'
        - icon classes for closed state
        - accept html: no
        - accept markdown: no
        - default: 'fa-duotone fa-lg fa-circle-chevron-right'
#}
