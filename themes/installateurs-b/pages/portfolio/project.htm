url = "/portfolio/:slug"
layout = "default"
title = "Project"

[PortfolioProjectDetail]
slug = "{{ :slug }}"

[PortfolioProjectList]
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $project = $this->components['PortfolioProjectDetail'];
$projectTitle = $project->project?->title;
$projectMetaDesc = $project->project?->seo['meta_description'];
$projectOgDesc = $project->project?->seo['og_description'];
$projectShortDesc = $project->project?->excerpt;

if (
isset($project->project?->seo['meta_title'])
&&
trim($project->project?->seo['meta_title']) !== ''
) {
$seoTitle = $project->project?->seo['meta_title'];
}

$seoDesc = $projectShortDesc;
if (
isset($projectOgDesc)
&&
trim($projectOgDesc) !== ''
) {
$seoDesc = $projectOgDesc;
}
if (
isset($projectMetaDesc)
&&
trim($projectMetaDesc) !== ''
) {
$seoDesc = $projectMetaDesc;
}

$seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
$title = $seoTitle ?? $projectTitle;
$this->page->title = $title . $seoTitleSuffix;
$this->page->meta_description = $seoDesc;
}

?>
==
{% if not PortfolioProjectDetail %}
    {% do abort(404) %}
{% endif %}

{% put styles %}
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <link rel="stylesheet" type="text/css" href="slick/slick-theme.css"/>

    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
    <style>
        .slick-current.slick-active { opacity: 1; }
        .slick-slide { opacity: .5; }
    </style>
{% endput %}

{% put scripts %}
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script>
        $('.pf-slider-main').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            fade: true,
            asNavFor: '.pf-slider-trail',
            {% if projectImages|length <= 3 %}swipe: false,{% endif %}
        });
        $('.pf-slider-trail').slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            asNavFor: '.pf-slider-main',
            {% if projectImages|length > 3 %}
            centerMode: true,
            {% endif %}
            dots: false,
            focusOnSelect: true,
            variableWidth: true,
            prevArrow: '#pf-slider-trail-prev',
            nextArrow: '#pf-slider-trail-next',
        });
    </script>
{% endput %}


{% set categories = PortfolioProjectDetail.categories %}
{% set projects = PortfolioProjectDetail.projects.sortByDesc('id') %}
{% set projectPage = PortfolioProjectDetail.projectPage %}
{% set categoryPage = PortfolioProjectDetail.categoryPage %}
{% set portfolioPage = PortfolioProjectDetail.portfolioPage %}
{% set projectImages = project.images %}
{% if project.gallery_type == 'folder' %}
    {% set projectImages = projectFolderImages %}
{% endif %}

<article
    data-name="project-detail"
    data-category="portfolio"
    class="bg-gray-100 {{ dark }} dark:bg-gray-700">

    <div class="container">

        <div class="space-y-4 lg:space-y-0 lg:grid lg:grid-cols-12 lg:gap-4 xl:gap-x-8 xl:gap-y-4 py-8 lg:py-20">

            <div class="col-span-12 pl-2">
                {% partial 'atomic/atoms/portfolio/portfolio-breadcrumbs' portfolioPage=portfolioPage %}
            </div>

            <div class="col-span-8 space-y-8 bg-white dark:bg-gray-500 md:rounded-lg py-8 px-4 -mx-4 md:mx-0 md:px-8">

                <div class="flex flex-wrap items-center gap-2">
                    {% for item in project.categories %}
                        {% partial 'atomic/atoms/batch-link' label=item.title link=categoryPage | page({ slug: item.slug }) %}
                    {% endfor %}
                </div>

                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                    {% partial 'atomic/atoms/headings/header-h1' text=project.title %}
                </div>

                <div class="space-y-8">

                    {% if project.excerpt %}
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            {{ project.excerpt | content }}
                        </div>
                    {% endif %}

                    {% if project.details %}
                        <div class="py-3 px-4 rounded-lg bg-gray-100 my-8">
                            <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-3 md:gap-8">
                                {% for detail in project.details %}
                                    <div>
                                        <p>
                                            <b>{{ detail.label }}</b><br>
                                            <span class="text-sm">
                                            {{ detail.text }}
                                        </span>
                                        </p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    {% partial 'atomic/molecules/portfolio/project-slider' %}


                    {% if project.description %}
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            {% partial 'atomic/molecules/content-section' content=project.description %}
                        </div>
                    {% endif %}



                </div>

                <div class="border-t border-gray-500 dark:border-gray-200">
                    <div class="group pt-4">
                        <a href="{{ portfolioPage | link }}" class="flex items-center gap-2">
                            <i class="fa-regular fa-circle-chevron-left group-hover:text-primary dark:text-gray-50 dark:group-hover:text-primary-highlight"></i>
                            <span class="underline group-hover:text-primary dark:text-gray-50 dark:group-hover:text-primary-highlight">Terug naar het overzicht</span>
                        </a>
                    </div>
                </div>

            </div>

            {% partial 'atomic/organisms/portfolio/portfolio-sidebar' categories=categories %}

        </div>
    </div>
</article>

{# Add SEO meta tags to page #}
{% set seo_object = project %}
{% partial 'site/seo_meta' item = seo_object %}
