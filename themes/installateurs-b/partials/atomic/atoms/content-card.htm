<div class="prose prose-primary dark:prose-primary_inverted max-w-none prose-p:text-gray-600 dark:prose-p:text-gray-100 {{ class }}">
    {% if truncate %}
        {{ text | content | html_limit(truncate, '...') }}
    {% else %}
        {{ text | content }}
    {% endif %}
</div>

{#
    accepted variables:

    'text'
        - text to display
        - default: null
        - accept html: yes

    'truncate'
        - max. characters in text variable
        - type: number
        - default: null

    'class'
        - CSS class, can be Tailwind CSS classes
        - default: null
#}
