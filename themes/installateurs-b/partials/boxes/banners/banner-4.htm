<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="banner-4"
    data-category="banners"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="max-w-4xl mx-auto px-4 text-center space-y-8">
            <div class="prose prose-primary dark:prose-primary_inverted max-w-none dark:prose-p:text-white">
                {% partial 'atomic/molecules/content-heading' %}
                {% partial 'atomic/molecules/content-section' content=box.content %}
            </div>

            <div class="flex justify-center">
                {% if box.big_link == 'tel' %}
                    <a href="tel:{{ company.telephone }}" class="text-3xl flex items-center space-x-3 dark:text-white group" title="Bel ons!">
                        <i class="fa-solid fa-phone group-hover:rotate-12 group-hover:text-tertiary transition-all"></i>
                        <span>{{ company.telephone }}</span>
                    </a>
                {% elseif box.big_link == 'email' %}
                    <a href="mailto:{{ company.email }}" class="text-3xl flex items-center space-x-3 dark:text-white group" title="E-mail ons!">
                        <i class="fa-solid fa-envelope group-hover:-translate-y-2 group-hover:text-tertiary transition-all"></i>
                        <span>{{ company.email }}</span>
                    </a>
                {% endif %}
            </div>

            <div class="flex justify-center">
                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons' buttons=box.buttons %}
                {% endif %}
            </div>
        </div>

</section>
