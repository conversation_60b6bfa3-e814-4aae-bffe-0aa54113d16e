<section
    data-name="text-double"
    data-category="text"
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }} "
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">


        <div class="container">
            <div class="flex flex-col gap-16 lg:grid lg:grid-cols-12">
                <div class="lg:col-span-6">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% partial 'atomic/molecules/content-section' content=box.text_left %}
                    </div>
                </div>
                <div class="lg:col-span-6">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% partial 'atomic/molecules/content-section' content=box.text_right %}
                    </div>
                </div>
            </div>
        </div>

</section>
