# Common
composer.phar
.DS_Store
.idea
.env
.env.*.php
.env.php
auth.json
php_errors.log
nginx-error.log
nginx-access.log
nginx-ssl.access.log
nginx-ssl.error.log
php-errors.log
sftp-config.json
.ftpconfig
selenium.php

package-lock.json
/node_modules
_ide_helper.php
.phpunit.result.cache
nbproject

# # ignore all themes
# /themes/*
# # except for child themes
# !/themes/*-child

# Project
/bootstrap/compiled.php
/vendor
/themes/**/content
/themes/**/meta
/themes/**/node_modules
storage/cms/blueprint-debug.php
storage/cms/blueprint-fieldsets.php
storage/cms/blueprint-permission.php
storage/cms/blueprint-sections.php
storage/cms/blueprint-navigation.php
storage/cms/blueprint-fields.php
/storage/cms/disabled.php

# Laravel Herd
herd.yml
