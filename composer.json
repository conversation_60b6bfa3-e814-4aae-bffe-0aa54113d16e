{"name": "invato/invato-basis", "description": "INVATO CMS: Built using October CMS: The Laravel-Based CMS Engineered For Simplicity", "type": "project", "homepage": "https://invato.nl", "license": "proprietary", "require": {"php": ">=8.3", "october/rain": "^3.0", "laravel/framework": "^10.0", "october/all": "^3.0", "rainlab/builder-plugin": "^2.0", "rainlab/pages-plugin": "^2.0", "rainlab/sitemap-plugin": "^1.2", "rainlab/translate-plugin": "^2.1", "offline/oc-boxes-plugin": "^3.0", "zen/robots-plugin": "^1.0", "renatio/formbuilder-plugin": "^4.0", "symfony/mailgun-mailer": "^6.0", "symfony/http-client": "^6.0", "rainlab/user-plugin": "^3.1", "janvince/smallgdpr-plugin": "^1.23", "offline/oc-site-search-plugin": "^1.7", "cyd293/backendskin-plugin": "^2.1", "renatio/spamprotection-plugin": "^1.0", "responsiv/currency-plugin": "^2.0", "renatio/dynamicpdf-plugin": "^7.1"}, "require-dev": {"laravel/pint": "^1.5", "laravel/sail": "^1.18", "phpunit/phpunit": "^8.0|^9.0", "roave/security-advisories": "dev-latest"}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate --ansi"], "post-autoload-dump": ["System\\Console\\ComposerScript::postAutoloadDump"], "post-update-cmd": ["System\\Console\\ComposerScript::postUpdateCmd"], "post-install-cmd": ["php artisan market:refresh -m"], "pre-package-uninstall": ["System\\Console\\ComposerScript::prePackageUninstall"], "test": ["phpunit --stop-on-failure"]}, "config": {"preferred-install": "dist", "allow-plugins": {"composer/installers": true}}, "autoload": {"psr-4": {"System\\Console\\": "modules/system/console"}, "classmap": ["market"]}, "minimum-stability": "dev", "prefer-stable": true, "repositories": {"octobercms": {"type": "composer", "url": "https://gateway.octobercms.com", "only": ["october/*", "*-plugin", "*-theme"]}}}