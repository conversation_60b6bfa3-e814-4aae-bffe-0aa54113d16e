<?php

namespace InvatoCore\Market\Observers;

use InvatoCore\Market\Console\Traits\CanHandlePluginsTrait;
use InvatoCore\Market\Models\MarketPlugin;
use JsonException;

class MarketPluginObserver
{
    use CanHandlePluginsTrait;

    /**
     * Wordt uitgevoerd na het bijwerken van een market plugin.
     *
     * @return void
     *
     * @throws JsonException
     */
    public function updated(MarketPlugin $marketPlugin)
    {
        if ($marketPlugin->isDirty('is_active')) {
            $this->refreshLinks();
            $this->migrateDatabase();
        }
    }
}
