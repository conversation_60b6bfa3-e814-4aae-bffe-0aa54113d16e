<?php

namespace InvatoCore\Market\Console;

use Illuminate\Console\Command;
use InvatoCore\Market\Console\Traits\CanHandlePluginsTrait;

/**
 * Class CreateSymlinksCommand
 */
class CreateSymlinksCommand extends Command
{
    use CanHandlePluginsTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'market:link';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create symlinks for all available market items';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->linkPlugins();
        $this->dumpAutoload();
    }
}
